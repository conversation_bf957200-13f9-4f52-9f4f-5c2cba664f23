import 'package:http/http.dart' as http;

class RouterDiscovery {
  // قائمة شاملة بعناوين الراوترات المحتملة
  static const List<String> possibleRouterIPs = [
    '***********',
    '***********',
    '***********',
    '*************',
    '***********',
    '************',
    '************',
    '************',
    '********',
    '********38',
    '********',
    '**********',
    '**********',
    '***********',
    '***********',
    '***********',
  ];

  static const List<String> commonPaths = [
    '/',
    '/index.html',
    '/index.html#home',
    '/index.html#login',
    '/login.html',
    '/main.html',
    '/admin.html',
    '/home.html',
  ];

  // اكتشاف عنوان الراوتر تلقائياً
  static Future<Map<String, dynamic>> discoverRouter() async {
    Map<String, dynamic> result = {
      'timestamp': DateTime.now().toIso8601String(),
      'found_routers': [],
      'detailed_results': [],
      'recommendations': [],
    };

    print('🔍 بدء اكتشاف الراوتر...');
    print('📡 سيتم اختبار ${possibleRouterIPs.length} عنوان مختلف');
    print('=' * 50);

    for (String ip in possibleRouterIPs) {
      Map<String, dynamic> ipResult = await _testRouterIP(ip);
      result['detailed_results'].add(ipResult);
      
      if (ipResult['accessible']) {
        result['found_routers'].add({
          'ip': ip,
          'working_paths': ipResult['working_paths'],
          'response_time': ipResult['avg_response_time'],
        });
        print('✅ وُجد راوتر في: $ip');
      }
    }

    _generateRecommendations(result);
    return result;
  }

  // اختبار عنوان IP محدد
  static Future<Map<String, dynamic>> _testRouterIP(String ip) async {
    Map<String, dynamic> result = {
      'ip': ip,
      'accessible': false,
      'working_paths': [],
      'response_times': [],
      'errors': [],
    };

    print('🌐 اختبار: $ip');

    for (String path in commonPaths) {
      try {
        String url = 'http://$ip$path';
        DateTime startTime = DateTime.now();
        
        final response = await http.get(
          Uri.parse(url),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
          },
        ).timeout(const Duration(seconds: 5));

        DateTime endTime = DateTime.now();
        int responseTime = endTime.difference(startTime).inMilliseconds;

        if (response.statusCode == 200) {
          result['accessible'] = true;
          result['working_paths'].add(path);
          result['response_times'].add(responseTime);
          
          // فحص إضافي للتأكد أنه راوتر ZTE
          String body = response.body.toLowerCase();
          if (body.contains('zte') || 
              body.contains('router') || 
              body.contains('login') ||
              body.contains('goform')) {
            print('  ✅ $path - راوتر ZTE محتمل (${responseTime}ms)');
          } else {
            print('  ✅ $path - يعمل (${responseTime}ms)');
          }
          
          // إذا وجدنا مسار يعمل، لا حاجة لتجربة المزيد
          break;
        }
      } catch (e) {
        result['errors'].add('$path: ${e.toString()}');
        continue;
      }
    }

    if (result['response_times'].isNotEmpty) {
      result['avg_response_time'] = 
          (result['response_times'] as List<int>).reduce((a, b) => a + b) ~/ 
          result['response_times'].length;
    }

    return result;
  }

  // إنشاء توصيات بناءً على النتائج
  static void _generateRecommendations(Map<String, dynamic> result) {
    List<String> recommendations = [];
    List<dynamic> foundRouters = result['found_routers'];

    if (foundRouters.isEmpty) {
      recommendations.add('❌ لم يتم العثور على أي راوتر');
      recommendations.add('🔧 الحلول المقترحة:');
      recommendations.add('   1. تأكد من اتصالك بشبكة الراوتر (WiFi أو كابل)');
      recommendations.add('   2. أعد تشغيل الراوتر (افصل الكهرباء 10 ثوان)');
      recommendations.add('   3. تحقق من إعدادات جدار الحماية');
      recommendations.add('   4. جرب من جهاز آخر للتأكد');
      recommendations.add('   5. ابحث عن عنوان IP في إعدادات الشبكة:');
      recommendations.add('      - Windows: ipconfig /all');
      recommendations.add('      - Android: Settings > WiFi > اسم الشبكة');
      recommendations.add('      - iPhone: Settings > WiFi > (i) بجانب الشبكة');
    } else {
      recommendations.add('✅ تم العثور على ${foundRouters.length} راوتر(ات):');
      
      for (var router in foundRouters) {
        String ip = router['ip'];
        List<dynamic> paths = router['working_paths'];
        int responseTime = router['response_time'] ?? 0;
        
        recommendations.add('');
        recommendations.add('🌐 الراوتر: http://$ip');
        recommendations.add('   المسارات العاملة: ${paths.join(', ')}');
        recommendations.add('   سرعة الاستجابة: ${responseTime}ms');
        recommendations.add('   💡 جرب: http://$ip${paths.first}');
      }
      
      recommendations.add('');
      recommendations.add('🎯 الخطوة التالية:');
      recommendations.add('   1. افتح المتصفح');
      recommendations.add('   2. اذهب لأفضل عنوان من القائمة أعلاه');
      recommendations.add('   3. جرب تسجيل الدخول بكلمة مرورك');
      recommendations.add('   4. إذا نجح، استخدم نفس العنوان في التطبيق');
    }

    result['recommendations'] = recommendations;
  }

  // تنسيق تقرير الاكتشاف
  static String formatDiscoveryReport(Map<String, dynamic> result) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🔍 تقرير اكتشاف الراوتر');
    report.writeln('=' * 50);
    report.writeln('⏰ الوقت: ${result['timestamp']}');
    report.writeln('📡 عدد العناوين المختبرة: ${possibleRouterIPs.length}');
    report.writeln();

    List<dynamic> foundRouters = result['found_routers'];
    
    if (foundRouters.isNotEmpty) {
      report.writeln('✅ الراوترات الموجودة:');
      for (var router in foundRouters) {
        report.writeln('  🌐 http://${router['ip']}');
        report.writeln('     المسارات: ${router['working_paths'].join(', ')}');
        report.writeln('     السرعة: ${router['response_time']}ms');
        report.writeln();
      }
    } else {
      report.writeln('❌ لم يتم العثور على أي راوتر');
      report.writeln();
    }

    report.writeln('💡 التوصيات:');
    for (String rec in result['recommendations']) {
      report.writeln('  $rec');
    }

    report.writeln();
    report.writeln('📊 تفاصيل الاختبار:');
    
    List<dynamic> detailedResults = result['detailed_results'];
    int workingCount = detailedResults.where((r) => r['accessible']).length;
    int failedCount = detailedResults.length - workingCount;
    
    report.writeln('  ✅ عناوين تعمل: $workingCount');
    report.writeln('  ❌ عناوين لا تعمل: $failedCount');
    report.writeln('  📈 معدل النجاح: ${(workingCount / detailedResults.length * 100).toStringAsFixed(1)}%');

    return report.toString();
  }

  // اختبار سريع لعنوان محدد
  static Future<bool> quickTest(String ip) async {
    try {
      final response = await http.get(
        Uri.parse('http://$ip'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      ).timeout(const Duration(seconds: 3));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
