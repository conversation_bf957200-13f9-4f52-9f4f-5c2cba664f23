# دليل حل المشاكل - ZTE Router Control

## 🆕 أداة التشخيص الجديدة!

**قبل المتابعة مع الحلول اليدوية، جرب أداة التشخيص المدمجة:**

1. افتح التطبيق
2. في شاشة تسجيل الدخول، اضغط **"تشخيص مشاكل تسجيل الدخول"**
3. أدخل كلمة مرور الراوتر واضغط **"بدء التشخيص"**
4. ستحصل على تقرير مفصل مع حلول مخصصة

📖 **للمزيد من التفاصيل**: راجع [دليل أداة التشخيص](LOGIN_DIAGNOSIS_GUIDE.md)

---

## مشكلة "كلمة المرور غير صحيحة"

### الأسباب المحتملة والحلول:

#### 1. كلمة المرور الخاطئة
**الحل:**
- جرب كلمات المرور الافتراضية الشائعة:
  - `admin`
  - `password`
  - `123456`
  - اتركها فارغة (لا تكتب شيء)
  - `root`

#### 2. عدم الاتصال بشبكة الراوتر
**الحل:**
- تأكد من اتصال جهازك بشبكة الواي فاي الخاصة بالراوتر
- أو اتصل بالراوتر عبر كابل إيثرنت
- تحقق من أن الراوتر يعمل بشكل طبيعي (الأضواء مضيئة)

#### 3. عنوان IP خاطئ للراوتر
**التطبيق يجرب تلقائياً العناوين التالية:**
- `***********` (الأكثر شيوعاً لراوترات ZTE)
- `***********`
- `***********`
- `********`

**للتحقق من العنوان الصحيح:**
1. افتح Command Prompt (cmd)
2. اكتب: `ipconfig`
3. ابحث عن "Default Gateway" - هذا هو عنوان الراوتر

#### 4. مشكلة في متصفح الويب
**الحل:**
- جرب فتح `http://***********` في المتصفح مباشرة
- إذا لم يفتح، فالمشكلة في الاتصال وليس في التطبيق

#### 5. راوتر من نوع مختلف
**بعض راوترات ZTE تستخدم طرق مختلفة:**
- بعضها لا يحتاج كلمة مرور
- بعضها يستخدم `admin/admin` كاسم مستخدم وكلمة مرور
- بعضها يستخدم الرقم التسلسلي ككلمة مرور

## مشاكل أخرى شائعة

### مشكلة "لا يمكن الاتصال بالراوتر"

#### الأسباب والحلول:
1. **الراوتر مغلق أو لا يعمل**
   - تحقق من أن الراوتر متصل بالكهرباء
   - تحقق من أن الأضواء مضيئة

2. **مشكلة في الشبكة**
   - أعد تشغيل الراوتر (افصل الكهرباء لـ 10 ثوان ثم أعد توصيلها)
   - أعد تشغيل جهازك

3. **جدار الحماية يحجب الاتصال**
   - أضف التطبيق لقائمة الاستثناءات في جدار الحماية
   - جرب إيقاف جدار الحماية مؤقتاً للاختبار

### مشكلة "التطبيق بطيء أو لا يستجيب"

#### الحلول:
1. **تحقق من قوة الإشارة**
   - اقترب من الراوتر
   - تأكد من عدم وجود عوائق

2. **أعد تشغيل التطبيق**
   - أغلق التطبيق وافتحه مرة أخرى

3. **أعد تشغيل الراوتر**
   - استخدم زر إعادة التشغيل في التطبيق
   - أو افصل الكهرباء وأعد توصيلها

### مشكلة "الأجهزة المتصلة لا تظهر"

#### الحلول:
1. **انتظر قليلاً**
   - قد يستغرق تحميل القائمة بعض الوقت

2. **اضغط زر التحديث**
   - استخدم زر التحديث في بطاقة الأجهزة المتصلة

3. **تحقق من إعدادات الراوتر**
   - بعض الراوترات تخفي الأجهزة المتصلة لأسباب أمنية

## نصائح للاستخدام الأمثل

### 1. كلمة المرور
- احتفظ بكلمة مرور الراوتر في مكان آمن
- غيّر كلمة المرور الافتراضية لأسباب أمنية

### 2. الاتصال
- استخدم اتصال واي فاي قوي للحصول على أفضل أداء
- تجنب استخدام التطبيق أثناء تحديث الراوتر

### 3. الأمان
- لا تشارك كلمة مرور الراوتر مع أشخاص غير موثوقين
- راقب الأجهزة المتصلة بانتظام

## الحصول على المساعدة

### معلومات مفيدة عند طلب المساعدة:
1. **موديل الراوتر**
   - ابحث عن الملصق على الراوتر
   - مثال: ZTE MF971V

2. **رسالة الخطأ الكاملة**
   - انسخ النص كاملاً من التطبيق

3. **خطوات إعادة إنتاج المشكلة**
   - اشرح بالتفصيل ما فعلته قبل ظهور المشكلة

4. **معلومات النظام**
   - نوع الجهاز (أندرويد، iOS، ويب، إلخ)
   - إصدار التطبيق

### طرق الحصول على المساعدة:
- افتح issue في GitHub
- راجع الوثائق في README.md
- تحقق من DEVELOPER_GUIDE.md للمطورين

## اختبار الاتصال

### طريقة سريعة للتحقق من الاتصال:
1. افتح المتصفح
2. اذهب إلى `http://***********`
3. إذا ظهرت صفحة تسجيل الدخول، فالاتصال يعمل
4. إذا لم تظهر، فالمشكلة في الشبكة وليس في التطبيق

### أوامر مفيدة في Command Prompt:
```cmd
# للتحقق من عنوان الراوتر
ipconfig

# للتحقق من الاتصال بالراوتر
ping ***********

# لعرض معلومات الشبكة
ipconfig /all
```

## ملاحظات مهمة

- **لا تستخدم التطبيق أثناء تحديث الراوتر**
- **احتفظ بنسخة احتياطية من إعدادات الراوتر**
- **تأكد من أن الراوتر يدعم الميزات المطلوبة**
- **بعض الميزات قد لا تعمل مع جميع موديلات ZTE**

---

إذا لم تحل هذه الحلول مشكلتك، يرجى فتح issue في GitHub مع تفاصيل المشكلة.
