import 'package:http/http.dart' as http;

class NetworkTester {
  static const List<String> routerUrls = [
    'http://***********',
    'http://***********',
    'http://***********',
    'http://********',
    'http://192.168.100.1',
    'http://192.168.2.1',
  ];

  static const List<String> testPaths = [
    '/index.html',
    '/',
    '/login.html',
    '/main.html',
    '/home.html',
  ];

  // اختبار شامل للشبكة
  static Future<Map<String, dynamic>> comprehensiveNetworkTest() async {
    Map<String, dynamic> results = {
      'timestamp': DateTime.now().toIso8601String(),
      'tests': {},
      'working_urls': [],
      'recommendations': [],
    };

    print('🔍 بدء اختبار الشبكة الشامل...');

    // 1. اختبار عناوين الراوتر المختلفة
    for (String baseUrl in routerUrls) {
      Map<String, dynamic> urlResult = await _testRouterUrl(baseUrl);
      results['tests'][baseUrl] = urlResult;
      
      if (urlResult['accessible']) {
        results['working_urls'].add(baseUrl);
        print('✅ تم العثور على راوتر في: $baseUrl');
      }
    }

    // 2. تحليل النتائج وإعطاء توصيات
    _analyzeNetworkResults(results);

    return results;
  }

  // اختبار عنوان راوتر محدد
  static Future<Map<String, dynamic>> _testRouterUrl(String baseUrl) async {
    Map<String, dynamic> result = {
      'url': baseUrl,
      'accessible': false,
      'response_times': [],
      'successful_paths': [],
      'errors': [],
    };

    for (String path in testPaths) {
      try {
        String fullUrl = '$baseUrl$path';
        DateTime startTime = DateTime.now();
        
        final response = await http.get(
          Uri.parse(fullUrl),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
          },
        ).timeout(const Duration(seconds: 10));

        DateTime endTime = DateTime.now();
        int responseTime = endTime.difference(startTime).inMilliseconds;

        if (response.statusCode == 200) {
          result['accessible'] = true;
          result['successful_paths'].add(path);
          result['response_times'].add(responseTime);
          
          print('✅ $fullUrl - ${response.statusCode} (${responseTime}ms)');
          break; // وجدنا مسار يعمل، لا حاجة لتجربة المزيد
        }
      } catch (e) {
        result['errors'].add('$path: ${e.toString()}');
        print('❌ $baseUrl$path - خطأ: $e');
      }
    }

    return result;
  }

  // تحليل نتائج اختبار الشبكة
  static void _analyzeNetworkResults(Map<String, dynamic> results) {
    List<String> workingUrls = List<String>.from(results['working_urls']);
    List<String> recommendations = [];

    if (workingUrls.isEmpty) {
      recommendations.add('❌ لم يتم العثور على أي راوتر');
      recommendations.add('🔧 الحلول المقترحة:');
      recommendations.add('   1. تأكد من اتصالك بشبكة الراوتر (WiFi أو كابل)');
      recommendations.add('   2. تحقق من أن الراوتر مشغل ويعمل');
      recommendations.add('   3. جرب إعادة تشغيل الراوتر');
      recommendations.add('   4. تحقق من إعدادات جدار الحماية');
      recommendations.add('   5. جرب من جهاز آخر للتأكد');
      recommendations.add('   6. ابحث عن عنوان IP الصحيح في إعدادات الشبكة');
    } else {
      recommendations.add('✅ تم العثور على راوتر(ات) في:');
      for (String url in workingUrls) {
        recommendations.add('   • $url');
      }
      recommendations.add('💡 استخدم أحد هذه العناوين في التطبيق');
    }

    results['recommendations'] = recommendations;
  }

  // اختبار سريع للاتصال
  static Future<String?> quickConnectionTest() async {
    print('🚀 اختبار سريع للاتصال...');
    
    for (String baseUrl in routerUrls) {
      try {
        final response = await http.get(
          Uri.parse('$baseUrl/index.html'),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          },
        ).timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          print('✅ اتصال سريع ناجح: $baseUrl');
          return baseUrl;
        }
      } catch (e) {
        continue;
      }
    }
    
    print('❌ فشل الاتصال السريع');
    return null;
  }

  // فحص إعدادات الشبكة المحلية
  static Future<Map<String, dynamic>> checkLocalNetworkSettings() async {
    Map<String, dynamic> networkInfo = {
      'timestamp': DateTime.now().toIso8601String(),
      'local_ip': 'Unknown',
      'gateway': 'Unknown',
      'dns': 'Unknown',
      'recommendations': [],
    };

    try {
      // محاولة الحصول على معلومات الشبكة من خلال اختبار عناوين مختلفة
      List<String> possibleGateways = [
        '***********',
        '***********', 
        '***********',
        '********',
      ];

      for (String gateway in possibleGateways) {
        try {
          final response = await http.get(
            Uri.parse('http://$gateway'),
            headers: {'User-Agent': 'NetworkTester'},
          ).timeout(const Duration(seconds: 3));

          if (response.statusCode == 200) {
            networkInfo['gateway'] = gateway;
            networkInfo['recommendations'].add('✅ تم العثور على Gateway: $gateway');
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (networkInfo['gateway'] == 'Unknown') {
        networkInfo['recommendations'].add('❌ لم يتم العثور على Gateway');
        networkInfo['recommendations'].add('🔧 تحقق من إعدادات الشبكة في النظام');
      }

    } catch (e) {
      networkInfo['recommendations'].add('❌ خطأ في فحص إعدادات الشبكة: $e');
    }

    return networkInfo;
  }

  // تقرير شامل للشبكة
  static String formatNetworkReport(Map<String, dynamic> results) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🌐 تقرير اختبار الشبكة الشامل');
    report.writeln('=' * 50);
    report.writeln('⏰ الوقت: ${results['timestamp']}');
    report.writeln();

    // النتائج
    if (results['working_urls'].isNotEmpty) {
      report.writeln('✅ راوترات تم العثور عليها:');
      for (String url in results['working_urls']) {
        report.writeln('  • $url');
      }
    } else {
      report.writeln('❌ لم يتم العثور على أي راوتر');
    }

    report.writeln();

    // التوصيات
    report.writeln('📋 التوصيات:');
    for (String recommendation in results['recommendations']) {
      report.writeln('  $recommendation');
    }

    report.writeln();

    // تفاصيل الاختبارات
    report.writeln('📊 تفاصيل الاختبارات:');
    Map<String, dynamic> tests = results['tests'];
    
    for (var entry in tests.entries) {
      String url = entry.key;
      Map<String, dynamic> test = entry.value;
      
      report.writeln('  🔗 $url');
      report.writeln('     الحالة: ${test['accessible'] ? '✅ متاح' : '❌ غير متاح'}');
      
      if (test['successful_paths'].isNotEmpty) {
        report.writeln('     المسارات الناجحة: ${test['successful_paths'].join(', ')}');
      }
      
      if (test['response_times'].isNotEmpty) {
        int avgTime = (test['response_times'] as List<int>).reduce((a, b) => a + b) ~/ test['response_times'].length;
        report.writeln('     متوسط وقت الاستجابة: ${avgTime}ms');
      }
      
      if (test['errors'].isNotEmpty) {
        report.writeln('     الأخطاء: ${test['errors'].length}');
      }
      
      report.writeln();
    }

    return report.toString();
  }
}
