import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/router_provider.dart';
import '../models/bandwidth_settings.dart';

class BandwidthControlCard extends StatelessWidget {
  const BandwidthControlCard({super.key});

  void _showBandwidthDialog(BuildContext context) {
    final provider = context.read<RouterProvider>();
    final currentSettings = provider.bandwidthSettings ?? 
        BandwidthSettings(uploadLimit: 0, downloadLimit: 0, isEnabled: false);

    showDialog(
      context: context,
      builder: (context) => _BandwidthSettingsDialog(currentSettings: currentSettings),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RouterProvider>(
      builder: (context, provider, child) {
        final bandwidthSettings = provider.bandwidthSettings;
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.speed,
                        color: Colors.orange,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'التحكم في سرعة الإنترنت',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'تحديد سرعة الرفع والتحميل',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                if (bandwidthSettings != null) ...[
                  // حالة التحكم في السرعة
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: bandwidthSettings.isEnabled 
                          ? Colors.orange[50] 
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: bandwidthSettings.isEnabled 
                            ? Colors.orange[200]! 
                            : Colors.grey[200]!,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              bandwidthSettings.isEnabled 
                                  ? Icons.speed 
                                  : Icons.speed_outlined,
                              color: bandwidthSettings.isEnabled 
                                  ? Colors.orange[600] 
                                  : Colors.grey[600],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              bandwidthSettings.isEnabled 
                                  ? 'التحكم في السرعة مُفعل' 
                                  : 'التحكم في السرعة مُعطل',
                              style: TextStyle(
                                color: bandwidthSettings.isEnabled 
                                    ? Colors.orange[700] 
                                    : Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        
                        if (bandwidthSettings.isEnabled) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'سرعة التحميل',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    Text(
                                      bandwidthSettings.downloadLimitText,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'سرعة الرفع',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                    Text(
                                      bandwidthSettings.uploadLimitText,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ] else ...[
                  // لا توجد إعدادات
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.speed_outlined,
                          color: Colors.grey[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'لم يتم تعيين حدود للسرعة',
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                const SizedBox(height: 16),
                
                // زر التعديل
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: !provider.isLoading
                        ? () => _showBandwidthDialog(context)
                        : null,
                    icon: const Icon(Icons.tune),
                    label: const Text('تعديل إعدادات السرعة'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // معلومات إضافية
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue[600],
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          'تحديد السرعة يؤثر على جميع الأجهزة المتصلة',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _BandwidthSettingsDialog extends StatefulWidget {
  final BandwidthSettings currentSettings;

  const _BandwidthSettingsDialog({required this.currentSettings});

  @override
  State<_BandwidthSettingsDialog> createState() => _BandwidthSettingsDialogState();
}

class _BandwidthSettingsDialogState extends State<_BandwidthSettingsDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _downloadController;
  late TextEditingController _uploadController;
  late bool _isEnabled;

  @override
  void initState() {
    super.initState();
    _downloadController = TextEditingController(
      text: widget.currentSettings.downloadLimit > 0 
          ? widget.currentSettings.downloadLimit.toString() 
          : '',
    );
    _uploadController = TextEditingController(
      text: widget.currentSettings.uploadLimit > 0 
          ? widget.currentSettings.uploadLimit.toString() 
          : '',
    );
    _isEnabled = widget.currentSettings.isEnabled;
  }

  @override
  void dispose() {
    _downloadController.dispose();
    _uploadController.dispose();
    super.dispose();
  }

  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      final downloadLimit = int.tryParse(_downloadController.text) ?? 0;
      final uploadLimit = int.tryParse(_uploadController.text) ?? 0;

      final newSettings = BandwidthSettings(
        downloadLimit: downloadLimit,
        uploadLimit: uploadLimit,
        isEnabled: _isEnabled,
      );

      final provider = context.read<RouterProvider>();
      bool success = await provider.setBandwidthLimit(newSettings);

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث إعدادات السرعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                provider.errorMessage.isNotEmpty 
                    ? provider.errorMessage 
                    : 'فشل في تحديث إعدادات السرعة',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل إعدادات السرعة'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // تفعيل التحكم في السرعة
            SwitchListTile(
              title: const Text('تفعيل التحكم في السرعة'),
              subtitle: const Text('تحديد حدود السرعة لجميع الأجهزة'),
              value: _isEnabled,
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // سرعة التحميل
            TextFormField(
              controller: _downloadController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'سرعة التحميل (كيلوبت/ثانية)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.download),
                helperText: 'اتركه فارغاً للسرعة غير المحدودة',
              ),
              validator: (value) {
                if (_isEnabled && (value == null || value.isEmpty)) {
                  return null; // السرعة غير المحدودة مسموحة
                }
                if (value != null && value.isNotEmpty) {
                  final speed = int.tryParse(value);
                  if (speed == null || speed < 0) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // سرعة الرفع
            TextFormField(
              controller: _uploadController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'سرعة الرفع (كيلوبت/ثانية)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.upload),
                helperText: 'اتركه فارغاً للسرعة غير المحدودة',
              ),
              validator: (value) {
                if (_isEnabled && (value == null || value.isEmpty)) {
                  return null; // السرعة غير المحدودة مسموحة
                }
                if (value != null && value.isNotEmpty) {
                  final speed = int.tryParse(value);
                  if (speed == null || speed < 0) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                }
                return null;
              },
            ),
            
            const SizedBox(height: 8),
            
            // معلومات إضافية
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '1 ميجابت/ثانية = 1024 كيلوبت/ثانية',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        Consumer<RouterProvider>(
          builder: (context, provider, child) {
            return ElevatedButton(
              onPressed: provider.isLoading ? null : _saveSettings,
              child: provider.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('حفظ'),
            );
          },
        ),
      ],
    );
  }
}
