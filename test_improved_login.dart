import 'lib/utils/improved_login.dart';

void main() async {
  print('🚀 بدء اختبار تسجيل الدخول المحسن...\n');

  // جرب كلمة المرور الخاصة بك
  String password = 'Hd99884@'; // ضع كلمة مرورك هنا
  
  try {
    // تسجيل الدخول المحسن
    Map<String, dynamic> result = await ImprovedLogin.loginToRouter(password);
    
    // طباعة التقرير
    String report = ImprovedLogin.formatLoginReport(result);
    print(report);
    
    // إذا نجح تسجيل الدخول
    if (result['success']) {
      print('🎉 تم تسجيل الدخول بنجاح!');
      print('🌐 استخدم هذا العنوان: ${result['working_url']}');
      
      // يمكنك الآن استخدام الراوتر
      await testRouterFunctions(result['working_url']);
    } else {
      print('❌ فشل تسجيل الدخول');
      print('💡 جرب:');
      print('   1. تحقق من كلمة المرور');
      print('   2. تأكد من اتصالك بشبكة الراوتر');
      print('   3. جرب إعادة تشغيل الراوتر');
    }
    
  } catch (e) {
    print('❌ خطأ في تسجيل الدخول: $e');
  }
}

// اختبار وظائف الراوتر بعد تسجيل الدخول
Future<void> testRouterFunctions(String baseUrl) async {
  print('\n🔧 اختبار وظائف الراوتر...');
  
  try {
    // مثال: الحصول على معلومات الراوتر
    // يمكنك إضافة المزيد من الوظائف هنا
    print('✅ يمكنك الآن استخدام جميع وظائف الراوتر');
    
  } catch (e) {
    print('❌ خطأ في اختبار الوظائف: $e');
  }
}
