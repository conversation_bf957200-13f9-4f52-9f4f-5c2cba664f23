{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\zte\\zte\\android\\app\\.cxx\\RelWithDebInfo\\5q485048\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\zte\\zte\\android\\app\\.cxx\\RelWithDebInfo\\5q485048\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}