import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/router_provider.dart';
import '../models/wifi_settings.dart';

class WiFiSettingsCard extends StatelessWidget {
  const WiFiSettingsCard({super.key});

  void _showWiFiSettingsDialog(BuildContext context) {
    final provider = context.read<RouterProvider>();
    final currentSettings = provider.wifiSettings;
    
    if (currentSettings == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تحميل إعدادات الواي فاي'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _WiFiSettingsDialog(currentSettings: currentSettings),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RouterProvider>(
      builder: (context, provider, child) {
        final wifiSettings = provider.wifiSettings;
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.wifi,
                        color: Colors.blue,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إعدادات الواي فاي',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'عرض وتعديل إعدادات شبكة الواي فاي',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                if (wifiSettings != null) ...[
                  // اسم الشبكة
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.network_wifi,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'اسم الشبكة (SSID)',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          wifiSettings.ssid.isNotEmpty ? wifiSettings.ssid : 'غير محدد',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // معلومات إضافية
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: wifiSettings.isEnabled 
                                ? Colors.green[50] 
                                : Colors.red[50],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                wifiSettings.isEnabled 
                                    ? Icons.wifi 
                                    : Icons.wifi_off,
                                color: wifiSettings.isEnabled 
                                    ? Colors.green[600] 
                                    : Colors.red[600],
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                wifiSettings.isEnabled ? 'مُفعل' : 'مُعطل',
                                style: TextStyle(
                                  color: wifiSettings.isEnabled 
                                      ? Colors.green[700] 
                                      : Colors.red[700],
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.security,
                                color: Colors.blue[600],
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                wifiSettings.securityTypeArabic,
                                style: TextStyle(
                                  color: Colors.blue[700],
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // حالة التحميل أو عدم وجود بيانات
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Icon(
                          Icons.wifi_off,
                          color: Colors.grey[400],
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'لا يمكن تحميل إعدادات الواي فاي',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                const SizedBox(height: 16),
                
                // زر التعديل
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: wifiSettings != null && !provider.isLoading
                        ? () => _showWiFiSettingsDialog(context)
                        : null,
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل الإعدادات'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _WiFiSettingsDialog extends StatefulWidget {
  final WiFiSettings currentSettings;

  const _WiFiSettingsDialog({required this.currentSettings});

  @override
  State<_WiFiSettingsDialog> createState() => _WiFiSettingsDialogState();
}

class _WiFiSettingsDialogState extends State<_WiFiSettingsDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _ssidController;
  late TextEditingController _passwordController;
  late bool _isEnabled;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _ssidController = TextEditingController(text: widget.currentSettings.ssid);
    _passwordController = TextEditingController(text: widget.currentSettings.password);
    _isEnabled = widget.currentSettings.isEnabled;
  }

  @override
  void dispose() {
    _ssidController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      final newSettings = widget.currentSettings.copyWith(
        ssid: _ssidController.text,
        password: _passwordController.text,
        isEnabled: _isEnabled,
      );

      final provider = context.read<RouterProvider>();
      bool success = await provider.updateWiFiSettings(newSettings);

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث إعدادات الواي فاي بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                provider.errorMessage.isNotEmpty 
                    ? provider.errorMessage 
                    : 'فشل في تحديث إعدادات الواي فاي',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل إعدادات الواي فاي'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // اسم الشبكة
            TextFormField(
              controller: _ssidController,
              decoration: const InputDecoration(
                labelText: 'اسم الشبكة (SSID)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.network_wifi),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الشبكة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // كلمة المرور
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال كلمة المرور';
                }
                if (value.length < 8) {
                  return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // تفعيل الواي فاي
            SwitchListTile(
              title: const Text('تفعيل الواي فاي'),
              value: _isEnabled,
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        Consumer<RouterProvider>(
          builder: (context, provider, child) {
            return ElevatedButton(
              onPressed: provider.isLoading ? null : _saveSettings,
              child: provider.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('حفظ'),
            );
          },
        ),
      ],
    );
  }
}
