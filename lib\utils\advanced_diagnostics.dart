import 'dart:convert';
import 'package:http/http.dart' as http;

class AdvancedDiagnostics {
  static const String routerUrl = 'http://***********';
  
  // تشخيص متقدم يحاكي المتصفح بالضبط
  static Future<Map<String, dynamic>> diagnoseLoginProcess(String password) async {
    Map<String, dynamic> result = {
      'timestamp': DateTime.now().toIso8601String(),
      'password_tested': password,
      'steps': [],
      'detailed_responses': [],
      'recommendations': [],
      'success': false,
    };

    print('🔬 بدء التشخيص المتقدم...');
    print('🌐 الراوتر: $routerUrl');
    print('🔑 كلمة المرور: $password');
    print('=' * 50);

    try {
      // الخطوة 1: فحص الاتصال الأساسي
      await _step1_BasicConnectivity(result);
      
      // الخطوة 2: تحليل صفحة تسجيل الدخول
      await _step2_AnalyzeLoginPage(result);
      
      // الخطوة 3: محاكاة تسجيل الدخول بالضبط
      await _step3_SimulateBrowserLogin(result, password);
      
      // الخطوة 4: تحليل النتائج وإعطاء توصيات
      _step4_AnalyzeAndRecommend(result);
      
    } catch (e) {
      result['error'] = 'خطأ عام في التشخيص: $e';
      print('❌ خطأ عام: $e');
    }

    return result;
  }

  // الخطوة 1: فحص الاتصال الأساسي
  static Future<void> _step1_BasicConnectivity(Map<String, dynamic> result) async {
    print('\n🔍 الخطوة 1: فحص الاتصال الأساسي');
    result['steps'].add('فحص الاتصال الأساسي');

    List<String> urlsToTest = [
      '$routerUrl/',
      '$routerUrl/index.html',
      '$routerUrl/index.html#home',
      '$routerUrl/index.html#login',
    ];

    for (String url in urlsToTest) {
      try {
        print('🌐 اختبار: $url');
        
        final response = await http.get(
          Uri.parse(url),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
        ).timeout(const Duration(seconds: 10));

        Map<String, dynamic> responseInfo = {
          'url': url,
          'status_code': response.statusCode,
          'headers': response.headers,
          'body_length': response.body.length,
          'body_preview': response.body.length > 200 ? '${response.body.substring(0, 200)}...' : response.body,
        };

        result['detailed_responses'].add(responseInfo);
        
        if (response.statusCode == 200) {
          print('✅ $url - نجح (${response.statusCode})');
        } else {
          print('❌ $url - فشل (${response.statusCode})');
        }

      } catch (e) {
        print('❌ $url - خطأ: $e');
        result['detailed_responses'].add({
          'url': url,
          'error': e.toString(),
        });
      }
    }
  }

  // الخطوة 2: تحليل صفحة تسجيل الدخول
  static Future<void> _step2_AnalyzeLoginPage(Map<String, dynamic> result) async {
    print('\n🔍 الخطوة 2: تحليل صفحة تسجيل الدخول');
    result['steps'].add('تحليل صفحة تسجيل الدخول');

    try {
      final response = await http.get(
        Uri.parse('$routerUrl/index.html#login'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
          'Connection': 'keep-alive',
        },
      ).timeout(const Duration(seconds: 10));

      print('📄 تحليل محتوى الصفحة...');
      String body = response.body.toLowerCase();
      
      // البحث عن عناصر مهمة في الصفحة
      List<String> importantElements = [
        'login',
        'password',
        'username',
        'goform',
        'csrf',
        'token',
        'session',
        'captcha',
      ];

      Map<String, bool> foundElements = {};
      for (String element in importantElements) {
        foundElements[element] = body.contains(element);
        if (foundElements[element]!) {
          print('✅ وُجد: $element');
        }
      }

      result['page_analysis'] = {
        'status_code': response.statusCode,
        'content_length': response.body.length,
        'found_elements': foundElements,
        'cookies': response.headers['set-cookie'],
        'content_type': response.headers['content-type'],
      };

      // البحث عن نماذج تسجيل الدخول
      if (body.contains('form') || body.contains('goform')) {
        print('✅ وُجد نموذج تسجيل دخول');
      } else {
        print('⚠️ لم يوجد نموذج تسجيل دخول واضح');
      }

    } catch (e) {
      print('❌ خطأ في تحليل الصفحة: $e');
      result['page_analysis'] = {'error': e.toString()};
    }
  }

  // الخطوة 3: محاكاة تسجيل الدخول بالضبط
  static Future<void> _step3_SimulateBrowserLogin(Map<String, dynamic> result, String password) async {
    print('\n🔍 الخطوة 3: محاكاة تسجيل الدخول');
    result['steps'].add('محاكاة تسجيل الدخول');

    // أولاً: جلب الصفحة والكوكيز
    try {
      print('📄 جلب صفحة تسجيل الدخول...');
      final pageResponse = await http.get(
        Uri.parse('$routerUrl/index.html#login'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      );

      String? cookie = pageResponse.headers['set-cookie'];
      print('🍪 الكوكي: ${cookie ?? "غير موجود"}');

      // ثانياً: تجربة طرق تسجيل دخول مختلفة
      List<Map<String, dynamic>> loginAttempts = [
        // محاكاة دقيقة للمتصفح
        {
          'name': 'محاكاة المتصفح الدقيقة',
          'method': 'POST',
          'url': '$routerUrl/goform/goform_set_cmd_process',
          'headers': {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': cookie ?? '',
            'Host': '***********',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        
        // طريقة مبسطة
        {
          'name': 'طريقة مبسطة',
          'method': 'POST',
          'url': '$routerUrl/goform/goform_set_cmd_process',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Referer': '$routerUrl/index.html#login',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },

        // بدون تشفير
        {
          'name': 'بدون تشفير Base64',
          'method': 'POST',
          'url': '$routerUrl/goform/goform_set_cmd_process',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Referer': '$routerUrl/index.html#login',
          },
          'body': {
            'goformId': 'LOGIN',
            'password': password,
          },
        },
      ];

      // تجربة كل طريقة
      for (var attempt in loginAttempts) {
        try {
          print('\n🔐 تجربة: ${attempt['name']}');
          
          final loginResponse = await http.post(
            Uri.parse(attempt['url']),
            headers: Map<String, String>.from(attempt['headers']),
            body: Map<String, String>.from(attempt['body']),
          ).timeout(const Duration(seconds: 15));

          Map<String, dynamic> attemptResult = {
            'name': attempt['name'],
            'status_code': loginResponse.statusCode,
            'headers': loginResponse.headers,
            'body': loginResponse.body,
            'success': _isLoginSuccessful(loginResponse.body),
          };

          result['detailed_responses'].add(attemptResult);

          print('📊 Status: ${loginResponse.statusCode}');
          print('📄 Response: ${loginResponse.body}');
          print('✅ نجح؟ ${attemptResult['success']}');

          if (attemptResult['success']) {
            result['success'] = true;
            result['successful_method'] = attempt['name'];
            print('🎉 نجح تسجيل الدخول!');
            return;
          }

        } catch (e) {
          print('❌ خطأ في ${attempt['name']}: $e');
          result['detailed_responses'].add({
            'name': attempt['name'],
            'error': e.toString(),
          });
        }
      }

    } catch (e) {
      print('❌ خطأ في محاكاة تسجيل الدخول: $e');
    }
  }

  // الخطوة 4: تحليل النتائج وإعطاء توصيات
  static void _step4_AnalyzeAndRecommend(Map<String, dynamic> result) {
    print('\n🔍 الخطوة 4: تحليل النتائج');
    result['steps'].add('تحليل النتائج وإعطاء توصيات');

    List<String> recommendations = [];

    if (result['success']) {
      recommendations.add('✅ نجح تسجيل الدخول بطريقة: ${result['successful_method']}');
      recommendations.add('💡 استخدم هذه الطريقة في التطبيق الرئيسي');
    } else {
      recommendations.add('❌ فشل تسجيل الدخول بجميع الطرق');
      
      // تحليل الاستجابات للعثور على السبب
      bool hasConnectivity = false;
      bool hasValidResponse = false;
      
      for (var response in result['detailed_responses']) {
        if (response['status_code'] == 200) {
          hasConnectivity = true;
          hasValidResponse = true;
        }
      }

      if (!hasConnectivity) {
        recommendations.add('🔧 مشكلة في الاتصال - تحقق من:');
        recommendations.add('   - اتصالك بشبكة الراوتر');
        recommendations.add('   - أن الراوتر يعمل بشكل طبيعي');
        recommendations.add('   - إعدادات جدار الحماية');
      } else if (hasValidResponse) {
        recommendations.add('🔧 الاتصال يعمل لكن تسجيل الدخول فاشل - احتمالات:');
        recommendations.add('   - كلمة المرور غير صحيحة');
        recommendations.add('   - الراوتر يحتاج CSRF token');
        recommendations.add('   - الراوتر يحتاج captcha');
        recommendations.add('   - الراوتر مقفل بعد محاولات فاشلة');
        recommendations.add('   - طريقة تسجيل الدخول مختلفة');
      }

      recommendations.add('💡 جرب:');
      recommendations.add('   - تسجيل الدخول يدوياً في المتصفح');
      recommendations.add('   - فحص Network tab في Developer Tools');
      recommendations.add('   - إعادة تشغيل الراوتر');
      recommendations.add('   - تجربة كلمات مرور أخرى');
    }

    result['recommendations'] = recommendations;
  }

  // فحص نجاح تسجيل الدخول
  static bool _isLoginSuccessful(String responseBody) {
    try {
      final json = jsonDecode(responseBody);
      return json['result'] == 'success' || 
             json['result'] == '0' || 
             json['result'] == 0 ||
             json['success'] == true;
    } catch (e) {
      String body = responseBody.toLowerCase();
      return body.contains('success') && 
             !body.contains('error') && 
             !body.contains('fail');
    }
  }

  // تنسيق التقرير النهائي
  static String formatDetailedReport(Map<String, dynamic> result) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🔬 تقرير التشخيص المتقدم');
    report.writeln('=' * 60);
    report.writeln('⏰ الوقت: ${result['timestamp']}');
    report.writeln('🔑 كلمة المرور المختبرة: ${result['password_tested']}');
    report.writeln('🌐 الراوتر: $routerUrl');
    report.writeln();

    // النتيجة العامة
    if (result['success']) {
      report.writeln('🎉 النتيجة: نجح تسجيل الدخول!');
      report.writeln('🔧 الطريقة الناجحة: ${result['successful_method']}');
    } else {
      report.writeln('❌ النتيجة: فشل تسجيل الدخول');
    }

    report.writeln();

    // الخطوات المنفذة
    report.writeln('📋 الخطوات المنفذة:');
    for (int i = 0; i < result['steps'].length; i++) {
      report.writeln('  ${i + 1}. ${result['steps'][i]}');
    }

    report.writeln();

    // التوصيات
    report.writeln('💡 التوصيات:');
    for (String rec in result['recommendations']) {
      report.writeln('  $rec');
    }

    report.writeln();

    // تفاصيل الاستجابات (مختصرة)
    report.writeln('📊 ملخص الاستجابات:');
    for (var response in result['detailed_responses']) {
      if (response['name'] != null) {
        report.writeln('  🔐 ${response['name']}:');
        report.writeln('     Status: ${response['status_code'] ?? 'خطأ'}');
        if (response['body'] != null) {
          String body = response['body'].toString();
          String preview = body.length > 100 ? '${body.substring(0, 100)}...' : body;
          report.writeln('     Response: $preview');
        }
        report.writeln();
      }
    }

    return report.toString();
  }
}
