# ZTE Router Control - ProGuard Rules for Android 15

# Keep Flutter classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-dontwarn io.flutter.**

# Keep HTTP classes for network requests
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# Keep JSON serialization classes
-keep class com.google.gson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*

# Keep network security classes
-keep class javax.net.ssl.** { *; }
-keep class org.apache.http.** { *; }
-dontwarn javax.net.ssl.**
-dontwarn org.apache.http.**

# Keep Android support libraries
-keep class androidx.** { *; }
-dontwarn androidx.**

# Keep WebView classes for router interface
-keep class android.webkit.** { *; }
-dontwarn android.webkit.**

# Keep reflection classes
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable classes
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Android 15 specific rules
-keep class android.app.** { *; }
-keep class android.content.** { *; }
-keep class android.net.** { *; }
-keep class android.os.** { *; }

# Keep notification classes for Android 15
-keep class android.app.NotificationManager { *; }
-keep class android.app.NotificationChannel { *; }
-keep class androidx.core.app.NotificationCompat { *; }

# Keep permission classes
-keep class android.permission.** { *; }
-keep class androidx.core.content.ContextCompat { *; }
-keep class androidx.core.app.ActivityCompat { *; }

# Optimize but don't obfuscate
-dontobfuscate
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Keep custom application class
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Keep activity classes
-keep public class * extends android.app.Activity
-keep public class * extends androidx.fragment.app.Fragment
-keep public class * extends android.app.Fragment

# Network security configuration
-keep class android.security.NetworkSecurityPolicy { *; }
-keep class android.security.net.config.** { *; }
