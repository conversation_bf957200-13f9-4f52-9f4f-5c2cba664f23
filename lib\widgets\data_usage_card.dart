import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/router_provider.dart';

class DataUsageCard extends StatelessWidget {
  const DataUsageCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RouterProvider>(
      builder: (context, provider, child) {
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  Colors.blue[50]!,
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.data_usage,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'استهلاك البيانات',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'إحصائيات الاستخدام اليومي والشهري',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // إحصائيات الاستخدام
                  Row(
                    children: [
                      Expanded(
                        child: _buildUsageItem(
                          context,
                          'اليوم',
                          '2.5 جيجابايت',
                          '↓ 1.8 GB  ↑ 0.7 GB',
                          Colors.green,
                          0.6,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildUsageItem(
                          context,
                          'هذا الشهر',
                          '45.2 جيجابايت',
                          '↓ 32.1 GB  ↑ 13.1 GB',
                          Colors.orange,
                          0.75,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات السرعة الحالية
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildSpeedItem(
                          context,
                          Icons.download,
                          'التحميل',
                          '15.2 Mbps',
                          Colors.green,
                        ),
                        Container(
                          height: 30,
                          width: 1,
                          color: Colors.blue[300],
                        ),
                        _buildSpeedItem(
                          context,
                          Icons.upload,
                          'الرفع',
                          '8.7 Mbps',
                          Colors.blue,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // إعادة تعيين الإحصائيات
                            _showResetDialog(context);
                          },
                          icon: const Icon(Icons.refresh, size: 16),
                          label: const Text('إعادة تعيين'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // عرض التفاصيل
                            _showDetailsDialog(context);
                          },
                          icon: const Icon(Icons.analytics, size: 16),
                          label: const Text('التفاصيل'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUsageItem(
    BuildContext context,
    String period,
    String total,
    String details,
    Color color,
    double progress,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            period,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            total,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: 6),
          Text(
            details,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpeedItem(
    BuildContext context,
    IconData icon,
    String label,
    String speed,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 11,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          speed,
          style: TextStyle(
            color: color,
            fontSize: 13,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإحصائيات'),
        content: const Text('هل تريد إعادة تعيين إحصائيات استهلاك البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين الإحصائيات'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showDetailsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل استهلاك البيانات'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('📊 الإحصائيات التفصيلية:'),
              SizedBox(height: 8),
              Text('• إجمالي البيانات المستهلكة: 45.2 GB'),
              Text('• متوسط الاستهلاك اليومي: 1.5 GB'),
              Text('• أعلى استهلاك في يوم: 3.2 GB'),
              Text('• التطبيقات الأكثر استهلاكاً: YouTube, Netflix'),
              SizedBox(height: 12),
              Text('⚡ معلومات السرعة:'),
              SizedBox(height: 8),
              Text('• أقصى سرعة تحميل: 25.6 Mbps'),
              Text('• أقصى سرعة رفع: 12.3 Mbps'),
              Text('• متوسط زمن الاستجابة: 45ms'),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
