import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/router_discovery.dart';

class RouterDiscoveryScreen extends StatefulWidget {
  const RouterDiscoveryScreen({super.key});

  @override
  State<RouterDiscoveryScreen> createState() => _RouterDiscoveryScreenState();
}

class _RouterDiscoveryScreenState extends State<RouterDiscoveryScreen> {
  bool _isDiscovering = false;
  Map<String, dynamic>? _discoveryResult;
  String? _discoveryReport;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اكتشاف الراوتر'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // شرح الأداة
            Card(
              color: Colors.teal[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.search, color: Colors.teal[600]),
                        const SizedBox(width: 8),
                        Text(
                          'اكتشاف عنوان الراوتر تلقائياً',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.teal[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الأداة ستبحث عن راوترك في جميع العناوين المحتملة:\n'
                      '• اختبار 16 عنوان مختلف للراوتر\n'
                      '• فحص 8 مسارات مختلفة لكل عنوان\n'
                      '• كشف راوترات ZTE تلقائياً\n'
                      '• تقرير مفصل مع العناوين العاملة',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // زر بدء الاكتشاف
            ElevatedButton.icon(
              onPressed: _isDiscovering ? null : _startDiscovery,
              icon: _isDiscovering 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.search),
              label: Text(_isDiscovering ? 'جاري البحث عن الراوتر...' : 'ابحث عن راوتري'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 16),

            // النتائج
            if (_discoveryResult != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتائج البحث',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _copyReport,
                                  icon: const Icon(Icons.copy),
                                  tooltip: 'نسخ التقرير',
                                ),
                                IconButton(
                                  onPressed: _openBestRouter,
                                  icon: const Icon(Icons.open_in_browser),
                                  tooltip: 'فتح أفضل راوتر',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildDiscoveryResults(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else if (!_isDiscovering) ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search,
                        size: 64,
                        color: Colors.teal[300],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اضغط "ابحث عن راوتري" لبدء البحث التلقائي',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'سيستغرق البحث 30-60 ثانية\nوسيختبر جميع العناوين المحتملة',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      const Text('جاري البحث عن الراوتر...'),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار، جاري اختبار جميع العناوين المحتملة\nقد يستغرق هذا دقيقة واحدة',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _startDiscovery() async {
    setState(() {
      _isDiscovering = true;
      _discoveryResult = null;
      _discoveryReport = null;
    });

    try {
      final result = await RouterDiscovery.discoverRouter();
      final report = RouterDiscovery.formatDiscoveryReport(result);

      setState(() {
        _discoveryResult = result;
        _discoveryReport = report;
        _isDiscovering = false;
      });
    } catch (e) {
      setState(() {
        _isDiscovering = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في البحث: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildDiscoveryResults() {
    if (_discoveryResult == null) return const SizedBox();

    List<Widget> widgets = [];
    List<dynamic> foundRouters = _discoveryResult!['found_routers'];

    // النتيجة العامة
    if (foundRouters.isNotEmpty) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  Text(
                    'تم العثور على ${foundRouters.length} راوتر!',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              for (var router in foundRouters) ...[
                Text('🌐 http://${router['ip']}'),
                Text('   المسارات: ${router['working_paths'].join(', ')}'),
                Text('   السرعة: ${router['response_time']}ms'),
                const SizedBox(height: 4),
              ],
            ],
          ),
        ),
      );
    } else {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'لم يتم العثور على أي راوتر',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      );
    }

    widgets.add(const SizedBox(height: 16));

    // التوصيات
    if (_discoveryResult!['recommendations'] != null) {
      widgets.add(
        const Text(
          'التوصيات:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
      widgets.add(const SizedBox(height: 8));

      for (String recommendation in _discoveryResult!['recommendations']) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(recommendation),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _copyReport() {
    if (_discoveryReport != null) {
      Clipboard.setData(ClipboardData(text: _discoveryReport!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ تقرير الاكتشاف إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _openBestRouter() {
    if (_discoveryResult != null) {
      List<dynamic> foundRouters = _discoveryResult!['found_routers'];
      if (foundRouters.isNotEmpty) {
        var bestRouter = foundRouters.first;
        String url = 'http://${bestRouter['ip']}${bestRouter['working_paths'].first}';
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('أفضل راوتر: $url\nانسخ هذا العنوان وافتحه في المتصفح'),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'نسخ',
              onPressed: () {
                Clipboard.setData(ClipboardData(text: url));
              },
            ),
          ),
        );
      }
    }
  }
}
