class WiFiSettings {
  final String ssid;
  final String password;
  final String securityType;
  final String frequency; // '2.4GHz' or '5GHz'
  final int channel;
  final bool isEnabled;
  final int maxConnections;
  final bool isHidden;

  WiFiSettings({
    required this.ssid,
    required this.password,
    required this.securityType,
    required this.frequency,
    required this.channel,
    required this.isEnabled,
    required this.maxConnections,
    required this.isHidden,
  });

  factory WiFiSettings.fromJson(Map<String, dynamic> json) {
    return WiFiSettings(
      ssid: json['ssid'] ?? json['wifi_ssid'] ?? '',
      password: json['password'] ?? json['wifi_password'] ?? '',
      securityType: json['security_type'] ?? json['auth_mode'] ?? 'WPA2',
      frequency: json['frequency'] ?? json['wifi_band'] ?? '2.4GHz',
      channel: int.tryParse(json['channel']?.toString() ?? '0') ?? 0,
      isEnabled: json['is_enabled'] == '1' || json['is_enabled'] == true,
      maxConnections: int.tryParse(json['max_connections']?.toString() ?? '32') ?? 32,
      isHidden: json['is_hidden'] == '1' || json['is_hidden'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ssid': ssid,
      'password': password,
      'security_type': securityType,
      'frequency': frequency,
      'channel': channel,
      'is_enabled': isEnabled,
      'max_connections': maxConnections,
      'is_hidden': isHidden,
    };
  }

  WiFiSettings copyWith({
    String? ssid,
    String? password,
    String? securityType,
    String? frequency,
    int? channel,
    bool? isEnabled,
    int? maxConnections,
    bool? isHidden,
  }) {
    return WiFiSettings(
      ssid: ssid ?? this.ssid,
      password: password ?? this.password,
      securityType: securityType ?? this.securityType,
      frequency: frequency ?? this.frequency,
      channel: channel ?? this.channel,
      isEnabled: isEnabled ?? this.isEnabled,
      maxConnections: maxConnections ?? this.maxConnections,
      isHidden: isHidden ?? this.isHidden,
    );
  }

  String get securityTypeArabic {
    switch (securityType.toUpperCase()) {
      case 'WPA2':
        return 'WPA2';
      case 'WPA3':
        return 'WPA3';
      case 'WEP':
        return 'WEP';
      case 'OPEN':
        return 'مفتوح';
      default:
        return securityType;
    }
  }

  String get frequencyArabic {
    switch (frequency) {
      case '2.4GHz':
        return '2.4 جيجاهرتز';
      case '5GHz':
        return '5 جيجاهرتز';
      default:
        return frequency;
    }
  }
}
