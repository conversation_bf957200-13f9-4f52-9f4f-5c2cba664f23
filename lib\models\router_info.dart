class RouterInfo {
  final String deviceName;
  final String firmwareVersion;
  final String hardwareVersion;
  final String imei;
  final String imsi;
  final String phoneNumber;
  final String networkType;
  final int signalStrength;
  final bool isConnected;
  final String ipAddress;
  final String macAddress;

  RouterInfo({
    required this.deviceName,
    required this.firmwareVersion,
    required this.hardwareVersion,
    required this.imei,
    required this.imsi,
    required this.phoneNumber,
    required this.networkType,
    required this.signalStrength,
    required this.isConnected,
    required this.ipAddress,
    required this.macAddress,
  });

  factory RouterInfo.fromJson(Map<String, dynamic> json) {
    return RouterInfo(
      deviceName: json['device_name'] ?? '',
      firmwareVersion: json['firmware_version'] ?? '',
      hardwareVersion: json['hardware_version'] ?? '',
      imei: json['imei'] ?? '',
      imsi: json['imsi'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      networkType: json['network_type'] ?? '',
      signalStrength: int.tryParse(json['signal_strength']?.toString() ?? '0') ?? 0,
      isConnected: json['is_connected'] == '1' || json['is_connected'] == true,
      ipAddress: json['ip_address'] ?? '',
      macAddress: json['mac_address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_name': deviceName,
      'firmware_version': firmwareVersion,
      'hardware_version': hardwareVersion,
      'imei': imei,
      'imsi': imsi,
      'phone_number': phoneNumber,
      'network_type': networkType,
      'signal_strength': signalStrength,
      'is_connected': isConnected,
      'ip_address': ipAddress,
      'mac_address': macAddress,
    };
  }
}
