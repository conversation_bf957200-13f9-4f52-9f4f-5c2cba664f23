// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:zte/main.dart';
import 'package:zte/providers/router_provider.dart';

void main() {
  testWidgets('ZTE Router Control app loads correctly', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ZTERouterApp());

    // Verify that the login screen is displayed
    expect(find.text('ZTE Router Control'), findsOneWidget);
    expect(find.text('تحكم كامل في راوتر ZTE الخاص بك'), findsOneWidget);
    expect(find.text('كلمة مرور الراوتر'), findsOneWidget);
    expect(find.byType(ElevatedButton), findsOneWidget);
  });

  testWidgets('Login form validation works', (WidgetTester tester) async {
    await tester.pumpWidget(const ZTERouterApp());

    // Try to login without entering password
    await tester.tap(find.byType(ElevatedButton));
    await tester.pump();

    // Verify validation message appears
    expect(find.text('يرجى إدخال كلمة المرور'), findsOneWidget);
  });

  testWidgets('Provider is properly initialized', (WidgetTester tester) async {
    await tester.pumpWidget(const ZTERouterApp());

    // Find the provider in the widget tree
    final BuildContext context = tester.element(find.byType(MaterialApp));
    final RouterProvider provider = Provider.of<RouterProvider>(
      context,
      listen: false,
    );

    // Verify initial state
    expect(provider.isLoggedIn, false);
    expect(provider.isLoading, false);
    expect(provider.errorMessage, '');
  });
}
