import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/network_tester.dart';

class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({super.key});

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  bool _isTesting = false;
  Map<String, dynamic>? _testResults;
  String? _networkReport;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الشبكة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // شرح الأداة
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.network_check, color: Colors.orange[600]),
                        const SizedBox(width: 8),
                        Text(
                          'اختبار الاتصال بالراوتر',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الأداة ستساعدك في:\n'
                      '• اختبار الاتصال بعناوين مختلفة للراوتر\n'
                      '• قياس سرعة الاستجابة\n'
                      '• تحديد العنوان الصحيح للراوتر\n'
                      '• تشخيص مشاكل الشبكة',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الاختبار
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTesting ? null : _runQuickTest,
                    icon: _isTesting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.speed),
                    label: const Text('اختبار سريع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTesting ? null : _runComprehensiveTest,
                    icon: _isTesting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.analytics),
                    label: const Text('اختبار شامل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // النتائج
            if (_testResults != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتائج اختبار الشبكة',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _copyReport,
                                  icon: const Icon(Icons.copy),
                                  tooltip: 'نسخ التقرير',
                                ),
                                IconButton(
                                  onPressed: _shareResults,
                                  icon: const Icon(Icons.share),
                                  tooltip: 'مشاركة النتائج',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildTestResults(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else if (!_isTesting) ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.network_check,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اختر نوع الاختبار لبدء تشخيص الشبكة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'الاختبار السريع: 5-10 ثوان\nالاختبار الشامل: 30-60 ثانية',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      const Text('جاري اختبار الاتصال بالراوتر...'),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار، قد يستغرق هذا بضع ثوان',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runQuickTest() async {
    setState(() {
      _isTesting = true;
      _testResults = null;
      _networkReport = null;
    });

    try {
      String? workingUrl = await NetworkTester.quickConnectionTest();
      
      Map<String, dynamic> results = {
        'type': 'quick',
        'timestamp': DateTime.now().toIso8601String(),
        'working_url': workingUrl,
        'recommendations': [],
      };

      if (workingUrl != null) {
        results['recommendations'].add('✅ تم العثور على راوتر في: $workingUrl');
        results['recommendations'].add('💡 يمكنك استخدام هذا العنوان في التطبيق');
      } else {
        results['recommendations'].add('❌ لم يتم العثور على أي راوتر');
        results['recommendations'].add('🔧 جرب الاختبار الشامل للمزيد من التفاصيل');
      }

      setState(() {
        _testResults = results;
        _isTesting = false;
      });
    } catch (e) {
      setState(() {
        _isTesting = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاختبار السريع: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _runComprehensiveTest() async {
    setState(() {
      _isTesting = true;
      _testResults = null;
      _networkReport = null;
    });

    try {
      final results = await NetworkTester.comprehensiveNetworkTest();
      final report = NetworkTester.formatNetworkReport(results);

      setState(() {
        _testResults = results;
        _networkReport = report;
        _isTesting = false;
      });
    } catch (e) {
      setState(() {
        _isTesting = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاختبار الشامل: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildTestResults() {
    if (_testResults == null) return const SizedBox();

    List<Widget> widgets = [];

    // نوع الاختبار
    String testType = _testResults!['type'] == 'quick' ? 'سريع' : 'شامل';
    widgets.add(
      Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text('نوع الاختبار: $testType'),
      ),
    );

    widgets.add(const SizedBox(height: 12));

    // النتائج الرئيسية
    if (_testResults!['working_urls'] != null && _testResults!['working_urls'].isNotEmpty) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  const Text(
                    'تم العثور على راوتر!',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              for (String url in _testResults!['working_urls'])
                Text('• $url'),
            ],
          ),
        ),
      );
    } else if (_testResults!['working_url'] != null) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  const Text(
                    'تم العثور على راوتر!',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('العنوان: ${_testResults!['working_url']}'),
            ],
          ),
        ),
      );
    } else {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'لم يتم العثور على أي راوتر',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      );
    }

    widgets.add(const SizedBox(height: 16));

    // التوصيات
    if (_testResults!['recommendations'] != null) {
      widgets.add(
        const Text(
          'التوصيات:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
      widgets.add(const SizedBox(height: 8));

      for (String recommendation in _testResults!['recommendations']) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(recommendation),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _copyReport() {
    if (_networkReport != null) {
      Clipboard.setData(ClipboardData(text: _networkReport!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ تقرير الشبكة إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (_testResults != null) {
      String simpleReport = 'تقرير اختبار الشبكة\n';
      simpleReport += '=' * 30 + '\n';
      simpleReport += 'الوقت: ${_testResults!['timestamp']}\n\n';
      
      if (_testResults!['working_url'] != null) {
        simpleReport += 'راوتر موجود في: ${_testResults!['working_url']}\n\n';
      }
      
      simpleReport += 'التوصيات:\n';
      for (String rec in _testResults!['recommendations']) {
        simpleReport += '$rec\n';
      }
      
      Clipboard.setData(ClipboardData(text: simpleReport));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ التقرير إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _shareResults() {
    // يمكن إضافة مشاركة النتائج هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استخدم زر النسخ لمشاركة النتائج'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
