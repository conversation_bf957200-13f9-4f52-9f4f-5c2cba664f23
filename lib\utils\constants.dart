class AppConstants {
  // معلومات التطبيق
  static const String appName = 'ZTE Router Control';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تحكم كامل في راوتر ZTE الخاص بك';

  // إعدادات الراوتر
  static const String routerBaseUrl = 'http://***********';
  static const String defaultPassword = 'admin';
  
  // مهلة الاتصال (بالثواني)
  static const int connectionTimeout = 10;
  static const int receiveTimeout = 15;
  
  // فترات التحديث (بالثواني)
  static const int deviceListRefreshInterval = 60;
  static const int routerInfoRefreshInterval = 30;
  
  // حدود السرعة (كيلوبت/ثانية)
  static const int minBandwidthLimit = 1;
  static const int maxBandwidthLimit = 100000; // 100 ميجابت
  
  // رسائل الخطأ
  static const String errorConnectionFailed = 'فشل في الاتصال بالراوتر';
  static const String errorInvalidPassword = 'كلمة المرور غير صحيحة';
  static const String errorTimeout = 'انتهت مهلة الاتصال';
  static const String errorUnknown = 'حدث خطأ غير متوقع';
  
  // رسائل النجاح
  static const String successLogin = 'تم تسجيل الدخول بنجاح';
  static const String successLogout = 'تم تسجيل الخروج بنجاح';
  static const String successReboot = 'تم إرسال أمر إعادة التشغيل';
  static const String successSettingsUpdated = 'تم تحديث الإعدادات بنجاح';
  
  // مفاتيح التخزين المحلي
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyLastLoginTime = 'last_login_time';
  static const String keyAutoRefresh = 'auto_refresh';
  
  // أنواع الاتصال
  static const String connectionTypeWireless = 'wireless';
  static const String connectionTypeWired = 'wired';
  
  // أنواع الأمان للواي فاي
  static const List<String> wifiSecurityTypes = [
    'WPA2',
    'WPA3',
    'WEP',
    'OPEN',
  ];
  
  // ترددات الواي فاي
  static const List<String> wifiFrequencies = [
    '2.4GHz',
    '5GHz',
  ];
}

class ApiEndpoints {
  // نقاط النهاية الأساسية
  static const String login = '/goform/goform_set_cmd_process';
  static const String getData = '/goform/goform_get_cmd_process';
  
  // معرفات الأوامر
  static const String loginCommand = 'LOGIN';
  static const String logoutCommand = 'LOGOUT';
  static const String rebootCommand = 'REBOOT_DEVICE';
  static const String setLteCommand = 'SET_LTE_USAGE_SWITCH';
  static const String setBandwidthCommand = 'SET_BANDWIDTH_LIMIT';
  static const String setWifiCommand = 'SET_WIFI_SETTINGS';
  
  // أوامر الحصول على البيانات
  static const String getDeviceInfo = 'device_info';
  static const String getNetworkInfo = 'network_info';
  static const String getStationList = 'station_list';
  static const String getWifiSettings = 'wifi_settings';
}

class AppColors {
  // الألوان الأساسية
  static const int primaryBlue = 0xFF1976D2;
  static const int secondaryBlue = 0xFF42A5F5;
  
  // ألوان الحالة
  static const int successGreen = 0xFF4CAF50;
  static const int warningOrange = 0xFFFF9800;
  static const int errorRed = 0xFFF44336;
  static const int infoBlue = 0xFF2196F3;
  
  // ألوان الخلفية
  static const int backgroundLight = 0xFFFAFAFA;
  static const int backgroundDark = 0xFF121212;
  static const int surfaceLight = 0xFFFFFFFF;
  static const int surfaceDark = 0xFF1E1E1E;
}

class AppSizes {
  // المسافات
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // أحجام الخط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 24.0;
  
  // أحجام الأيقونات
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  // نصف القطر للحواف المنحنية
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
}
