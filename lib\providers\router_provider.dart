import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/zte_api_service.dart';
import '../models/router_info.dart';
import '../models/connected_device.dart';
import '../models/wifi_settings.dart';
import '../models/bandwidth_settings.dart';

class RouterProvider extends ChangeNotifier {
  final ZTEApiService _apiService = ZTEApiService();

  // حالة التطبيق
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String _errorMessage = '';

  // بيانات الراوتر
  RouterInfo? _routerInfo;
  List<ConnectedDevice> _connectedDevices = [];
  WiFiSettings? _wifiSettings;
  BandwidthSettings? _bandwidthSettings;
  bool _lteEnabled = true;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  RouterInfo? get routerInfo => _routerInfo;
  List<ConnectedDevice> get connectedDevices => _connectedDevices;
  WiFiSettings? get wifiSettings => _wifiSettings;
  BandwidthSettings? get bandwidthSettings => _bandwidthSettings;
  bool get lteEnabled => _lteEnabled;

  // تسجيل الدخول
  Future<bool> login(String password) async {
    _setLoading(true);
    _clearError();

    try {
      bool success = await _apiService.login(password);
      if (success) {
        _isLoggedIn = true;
        await _saveLoginState(true);
        await loadInitialData();
      } else {
        _setError(
          'فشل في تسجيل الدخول. تحقق من:\n'
          '• كلمة المرور الصحيحة\n'
          '• الاتصال بشبكة الراوتر\n'
          '• عنوان الراوتر (192.168.0.1)\n'
          'جرب كلمات المرور: admin, password, 123456',
        );
      }
      return success;
    } catch (e) {
      String errorMsg = e.toString();
      if (errorMsg.contains('Failed to fetch') ||
          errorMsg.contains('Connection')) {
        _setError(
          'لا يمكن الاتصال بالراوتر.\n'
          'تأكد من:\n'
          '• الاتصال بشبكة الراوتر\n'
          '• عنوان الراوتر صحيح (192.168.0.1)\n'
          '• الراوتر يعمل بشكل طبيعي',
        );
      } else {
        _setError('خطأ في الاتصال بالراوتر: ${e.toString()}');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    if (!_isLoggedIn) return;

    _setLoading(true);
    try {
      await Future.wait([
        loadRouterInfo(),
        loadConnectedDevices(),
        loadWiFiSettings(),
      ]);
    } catch (e) {
      _setError('خطأ في تحميل البيانات');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل معلومات الراوتر
  Future<void> loadRouterInfo() async {
    try {
      _routerInfo = await _apiService.getRouterInfo();
      notifyListeners();
    } catch (e) {
      print('خطأ في تحميل معلومات الراوتر: $e');
    }
  }

  // تحميل الأجهزة المتصلة
  Future<void> loadConnectedDevices() async {
    try {
      _connectedDevices = await _apiService.getConnectedDevices();
      notifyListeners();
    } catch (e) {
      print('خطأ في تحميل الأجهزة المتصلة: $e');
    }
  }

  // تحميل إعدادات الواي فاي
  Future<void> loadWiFiSettings() async {
    try {
      _wifiSettings = await _apiService.getWiFiSettings();
      notifyListeners();
    } catch (e) {
      print('خطأ في تحميل إعدادات الواي فاي: $e');
    }
  }

  // تغيير حالة LTE
  Future<bool> toggleLTE(bool enabled) async {
    _setLoading(true);
    try {
      bool success = await _apiService.setLTEStatus(enabled);
      if (success) {
        _lteEnabled = enabled;
        notifyListeners();
      } else {
        _setError('فشل في تغيير حالة LTE');
      }
      return success;
    } catch (e) {
      _setError('خطأ في تغيير حالة LTE');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث إعدادات الواي فاي
  Future<bool> updateWiFiSettings(WiFiSettings settings) async {
    _setLoading(true);
    try {
      bool success = await _apiService.updateWiFiSettings(settings);
      if (success) {
        _wifiSettings = settings;
        notifyListeners();
      } else {
        _setError('فشل في تحديث إعدادات الواي فاي');
      }
      return success;
    } catch (e) {
      _setError('خطأ في تحديث إعدادات الواي فاي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديد سرعة الإنترنت
  Future<bool> setBandwidthLimit(BandwidthSettings settings) async {
    _setLoading(true);
    try {
      bool success = await _apiService.setBandwidthLimit(settings);
      if (success) {
        _bandwidthSettings = settings;
        notifyListeners();
      } else {
        _setError('فشل في تحديد سرعة الإنترنت');
      }
      return success;
    } catch (e) {
      _setError('خطأ في تحديد سرعة الإنترنت');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إعادة تشغيل الراوتر
  Future<bool> rebootRouter() async {
    _setLoading(true);
    try {
      bool success = await _apiService.rebootRouter();
      if (success) {
        // تسجيل الخروج بعد إعادة التشغيل
        await logout();
      } else {
        _setError('فشل في إعادة تشغيل الراوتر');
      }
      return success;
    } catch (e) {
      _setError('خطأ في إعادة تشغيل الراوتر');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    await _apiService.logout();
    _isLoggedIn = false;
    await _saveLoginState(false);
    _clearData();
    notifyListeners();
  }

  // حفظ حالة تسجيل الدخول
  Future<void> _saveLoginState(bool isLoggedIn) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_logged_in', isLoggedIn);
  }

  // تحميل حالة تسجيل الدخول
  Future<void> loadLoginState() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool('is_logged_in') ?? false;
    notifyListeners();
  }

  // تحديث البيانات
  Future<void> refreshData() async {
    if (_isLoggedIn) {
      await loadInitialData();
    }
  }

  // مساعدات خاصة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = '';
  }

  void _clearData() {
    _routerInfo = null;
    _connectedDevices.clear();
    _wifiSettings = null;
    _bandwidthSettings = null;
    _lteEnabled = true;
    _clearError();
  }
}
