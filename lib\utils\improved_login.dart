import 'dart:convert';
import 'package:http/http.dart' as http;

class ImprovedLogin {
  static const List<String> routerUrls = [
    'http://192.168.0.1',
    'http://192.168.1.1',
    'http://192.168.8.1',
    'http://10.0.0.1',
  ];

  // تسجيل دخول محسن مع معالجة أخطاء شاملة
  static Future<Map<String, dynamic>> loginToRouter(String password) async {
    Map<String, dynamic> result = {
      'success': false,
      'message': '',
      'working_url': null,
      'response_data': null,
      'attempts': [],
    };

    // جرب عناوين مختلفة للراوتر
    for (String baseUrl in routerUrls) {
      print('🔍 جاري تجربة العنوان: $baseUrl');
      
      Map<String, dynamic> attemptResult = await _attemptLogin(baseUrl, password);
      result['attempts'].add(attemptResult);
      
      if (attemptResult['success']) {
        result['success'] = true;
        result['working_url'] = baseUrl;
        result['message'] = 'نجح تسجيل الدخول!';
        result['response_data'] = attemptResult['response_data'];
        print('✅ نجح تسجيل الدخول مع العنوان: $baseUrl');
        return result;
      }
    }

    result['message'] = 'فشل تسجيل الدخول مع جميع العناوين المجربة';
    return result;
  }

  // محاولة تسجيل دخول لعنوان محدد
  static Future<Map<String, dynamic>> _attemptLogin(String baseUrl, String password) async {
    Map<String, dynamic> result = {
      'url': baseUrl,
      'success': false,
      'error': null,
      'response_data': null,
      'steps': [],
    };

    try {
      // الخطوة 1: جلب الصفحة الرئيسية والكوكي
      print('📄 جاري جلب الصفحة الرئيسية...');
      result['steps'].add('جلب الصفحة الرئيسية');
      
      final indexResponse = await http.get(
        Uri.parse('$baseUrl/index.html'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
        },
      ).timeout(const Duration(seconds: 10));

      if (indexResponse.statusCode != 200) {
        result['error'] = 'فشل في الوصول للصفحة الرئيسية: ${indexResponse.statusCode}';
        return result;
      }

      print('✅ تم جلب الصفحة الرئيسية بنجاح');

      // الخطوة 2: استخراج الكوكي
      result['steps'].add('استخراج الكوكي');
      String? cookie = indexResponse.headers['set-cookie'];
      
      cookie ??= indexResponse.headers['Set-Cookie'] ?? 
                indexResponse.headers['cookie'] ?? 
                indexResponse.headers['Cookie'];

      print('🍪 الكوكي: ${cookie ?? "غير موجود"}');

      // الخطوة 3: تجربة طرق تسجيل دخول مختلفة
      result['steps'].add('تجربة طرق تسجيل الدخول');
      
      List<Map<String, dynamic>> loginMethods = [
        // الطريقة الأساسية مع Base64
        {
          'name': 'Base64 مع Cookie',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        // طريقة بدون تشفير
        {
          'name': 'Plain Text مع Cookie',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': password,
          },
        },
        // طريقة بدون Cookie
        {
          'name': 'Base64 بدون Cookie',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        // طريقة مع username
        {
          'name': 'مع Username',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'username': 'admin',
            'password': base64Encode(utf8.encode(password)),
          },
        },
      ];

      // جرب كل طريقة
      for (var method in loginMethods) {
        try {
          print('🔐 جاري تجربة: ${method['name']}');
          
          final loginResponse = await http.post(
            Uri.parse('$baseUrl/goform/goform_set_cmd_process'),
            headers: Map<String, String>.from(method['headers']),
            body: Map<String, String>.from(method['body']),
          ).timeout(const Duration(seconds: 15));

          print('📊 Status Code: ${loginResponse.statusCode}');
          print('📄 Response: ${loginResponse.body}');

          if (loginResponse.statusCode == 200) {
            // فحص نجاح تسجيل الدخول
            bool loginSuccess = _checkLoginSuccess(loginResponse.body);
            
            if (loginSuccess) {
              result['success'] = true;
              result['response_data'] = {
                'status_code': loginResponse.statusCode,
                'body': loginResponse.body,
                'method': method['name'],
                'headers': loginResponse.headers,
              };
              print('✅ نجح تسجيل الدخول بطريقة: ${method['name']}');
              return result;
            } else {
              print('❌ فشل تسجيل الدخول بطريقة: ${method['name']}');
            }
          }
        } catch (e) {
          print('❌ خطأ في طريقة ${method['name']}: $e');
          continue;
        }
      }

      result['error'] = 'فشلت جميع طرق تسجيل الدخول';
      return result;

    } catch (e) {
      result['error'] = 'خطأ عام: $e';
      print('❌ خطأ عام في $baseUrl: $e');
      return result;
    }
  }

  // فحص نجاح تسجيل الدخول
  static bool _checkLoginSuccess(String responseBody) {
    try {
      // جرب تحليل JSON
      final jsonResponse = json.decode(responseBody);
      
      // فحص أشكال مختلفة للنجاح
      if (jsonResponse['result'] == 'success' ||
          jsonResponse['result'] == '0' ||
          jsonResponse['result'] == 0 ||
          jsonResponse['success'] == true ||
          jsonResponse['success'] == '1' ||
          jsonResponse['login'] == 'success') {
        return true;
      }

      // فحص أشكال الفشل
      if (jsonResponse['result'] == 'fail' ||
          jsonResponse['result'] == 'error' ||
          jsonResponse['result'] == '1' ||
          jsonResponse['result'] == 1 ||
          jsonResponse['error'] != null ||
          jsonResponse['login'] == 'fail') {
        return false;
      }

    } catch (e) {
      // ليس JSON، فحص نصي
    }

    // فحص نصي للمحتوى
    String body = responseBody.toLowerCase();
    
    // علامات النجاح
    bool hasSuccess = body.contains('success') ||
                     body.contains('ok') ||
                     body.contains('welcome');
    
    // علامات الفشل
    bool hasFailure = body.contains('error') ||
                     body.contains('fail') ||
                     body.contains('invalid') ||
                     body.contains('wrong') ||
                     body.contains('incorrect');

    // إذا كانت الاستجابة قصيرة وبدون أخطاء، قد تكون نجاح
    bool isShortSuccess = body.length < 50 && !hasFailure;

    return (hasSuccess || isShortSuccess) && !hasFailure;
  }

  // تقرير مفصل للنتائج
  static String formatLoginReport(Map<String, dynamic> result) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🔐 تقرير تسجيل الدخول المحسن');
    report.writeln('=' * 40);
    report.writeln('⏰ الوقت: ${DateTime.now().toIso8601String()}');
    report.writeln();

    if (result['success']) {
      report.writeln('✅ نجح تسجيل الدخول!');
      report.writeln('🌐 العنوان الناجح: ${result['working_url']}');
      
      if (result['response_data'] != null) {
        var responseData = result['response_data'];
        report.writeln('🔧 الطريقة الناجحة: ${responseData['method']}');
        report.writeln('📊 Status Code: ${responseData['status_code']}');
      }
    } else {
      report.writeln('❌ فشل تسجيل الدخول');
      report.writeln('💬 الرسالة: ${result['message']}');
    }

    report.writeln();
    report.writeln('📋 تفاصيل المحاولات:');
    
    List<dynamic> attempts = result['attempts'] ?? [];
    for (int i = 0; i < attempts.length; i++) {
      var attempt = attempts[i];
      report.writeln('  ${i + 1}. ${attempt['url']}');
      report.writeln('     النتيجة: ${attempt['success'] ? '✅ نجح' : '❌ فشل'}');
      if (attempt['error'] != null) {
        report.writeln('     الخطأ: ${attempt['error']}');
      }
      report.writeln();
    }

    return report.toString();
  }
}
