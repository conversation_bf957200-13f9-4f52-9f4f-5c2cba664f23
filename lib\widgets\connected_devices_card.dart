import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/router_provider.dart';
import '../models/connected_device.dart';

class ConnectedDevicesCard extends StatelessWidget {
  const ConnectedDevicesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RouterProvider>(
      builder: (context, provider, child) {
        final devices = provider.connectedDevices;

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.devices,
                        color: Colors.green,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الأجهزة المتصلة',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${devices.length} جهاز متصل حالياً',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed:
                          provider.isLoading
                              ? null
                              : () => provider.loadConnectedDevices(),
                      icon:
                          provider.isLoading
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Icon(Icons.refresh),
                      tooltip: 'تحديث قائمة الأجهزة',
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                if (devices.isEmpty) ...[
                  // لا توجد أجهزة متصلة
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Icon(
                          Icons.devices_other,
                          color: Colors.grey[400],
                          size: 48,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'لا توجد أجهزة متصلة',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'لم يتم العثور على أي أجهزة متصلة بالراوتر',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[500]),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  // قائمة الأجهزة المتصلة
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: devices.length,
                    separatorBuilder:
                        (context, index) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final device = devices[index];
                      return _DeviceListItem(device: device);
                    },
                  ),
                ],

                if (devices.isNotEmpty) ...[
                  const SizedBox(height: 12),

                  // معلومات إضافية
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue[600],
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            'يتم تحديث قائمة الأجهزة تلقائياً كل دقيقة',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

class _DeviceListItem extends StatelessWidget {
  final ConnectedDevice device;

  const _DeviceListItem({required this.device});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          // أيقونة نوع الجهاز
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getConnectionColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getConnectionIcon(),
              color: _getConnectionColor(),
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // معلومات الجهاز
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.deviceName,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                Text(
                  device.ipAddress,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getConnectionColor().withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        device.connectionTypeArabic,
                        style: TextStyle(
                          color: _getConnectionColor(),
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (device.connectionType.toLowerCase() == 'wireless') ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getSignalColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          device.signalStrengthText,
                          style: TextStyle(
                            color: _getSignalColor(),
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // قوة الإشارة (للأجهزة اللاسلكية)
          if (device.connectionType.toLowerCase() == 'wireless')
            Column(
              children: [
                Icon(_getSignalIcon(), color: _getSignalColor(), size: 16),
                const SizedBox(height: 2),
                Text(
                  '${device.signalStrength}%',
                  style: TextStyle(
                    color: _getSignalColor(),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Color _getConnectionColor() {
    switch (device.connectionType.toLowerCase()) {
      case 'wireless':
      case 'wifi':
        return Colors.blue;
      case 'wired':
      case 'ethernet':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getConnectionIcon() {
    switch (device.connectionType.toLowerCase()) {
      case 'wireless':
      case 'wifi':
        return Icons.wifi;
      case 'wired':
      case 'ethernet':
        return Icons.cable;
      default:
        return Icons.device_unknown;
    }
  }

  Color _getSignalColor() {
    if (device.signalStrength >= 80) return Colors.green;
    if (device.signalStrength >= 60) return Colors.lightGreen;
    if (device.signalStrength >= 40) return Colors.orange;
    if (device.signalStrength >= 20) return Colors.deepOrange;
    return Colors.red;
  }

  IconData _getSignalIcon() {
    if (device.signalStrength >= 80) return Icons.signal_wifi_4_bar;
    if (device.signalStrength >= 60) return Icons.signal_wifi_4_bar;
    if (device.signalStrength >= 40) return Icons.wifi;
    if (device.signalStrength >= 20) return Icons.wifi;
    return Icons.signal_wifi_0_bar;
  }
}
