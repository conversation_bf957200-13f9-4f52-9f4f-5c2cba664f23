# دليل المطور - ZTE Router Control

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة البداية الرئيسية
├── models/                   # نماذج البيانات
│   ├── router_info.dart
│   ├── connected_device.dart
│   ├── wifi_settings.dart
│   └── bandwidth_settings.dart
├── providers/                # إدارة الحالة
│   └── router_provider.dart
├── screens/                  # الشاشات
│   ├── login_screen.dart
│   └── home_screen.dart
├── services/                 # خدمات API
│   └── zte_api_service.dart
├── widgets/                  # المكونات المخصصة
│   ├── lte_control_card.dart
│   ├── wifi_settings_card.dart
│   ├── connected_devices_card.dart
│   ├── bandwidth_control_card.dart
│   └── router_actions_card.dart
└── utils/                    # الأدوات المساعدة
    ├── constants.dart
    ├── validators.dart
    └── formatters.dart
```

## المعمارية

### 1. Provider Pattern
- استخدام `RouterProvider` لإدارة حالة التطبيق
- فصل منطق العمل عن واجهة المستخدم
- إشعارات تلقائية عند تغيير البيانات

### 2. Service Layer
- `ZTEApiService` للتواصل مع API الراوتر
- معالجة الأخطاء والاستثناءات
- إدارة الجلسات والكوكيز

### 3. Model Classes
- نماذج بيانات منظمة مع `fromJson` و `toJson`
- تحويل البيانات من وإلى JSON
- خصائص محسوبة للعرض

## API الراوتر

### تسجيل الدخول
```dart
POST /goform/goform_set_cmd_process
Content-Type: application/x-www-form-urlencoded

isTest=false
goformId=LOGIN
password=Base64(admin)
```

### التحكم في LTE
```dart
POST /goform/goform_set_cmd_process

isTest=false
goformId=SET_LTE_USAGE_SWITCH
lte_usage_switch_enable=1  // 1 للتشغيل، 0 للإيقاف
```

### الحصول على الأجهزة المتصلة
```dart
GET /goform/goform_get_cmd_process?multi_data=1&cmd=station_list
```

### تحديد سرعة الإنترنت
```dart
POST /goform/goform_set_cmd_process

isTest=false
goformId=SET_BANDWIDTH_LIMIT
upload_limit=100
download_limit=150
BandwidthLimitSwitch=1
```

### إعادة تشغيل الراوتر
```dart
POST /goform/goform_set_cmd_process

isTest=false
goformId=REBOOT_DEVICE
```

## إضافة ميزات جديدة

### 1. إضافة نموذج بيانات جديد
```dart
// lib/models/new_model.dart
class NewModel {
  final String property1;
  final int property2;

  NewModel({required this.property1, required this.property2});

  factory NewModel.fromJson(Map<String, dynamic> json) {
    return NewModel(
      property1: json['property1'] ?? '',
      property2: int.tryParse(json['property2']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'property1': property1,
      'property2': property2,
    };
  }
}
```

### 2. إضافة وظيفة API جديدة
```dart
// في ZTEApiService
Future<bool> newApiFunction(String parameter) async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl$loginEndpoint'),
      headers: _getHeaders(),
      body: {
        'isTest': 'false',
        'goformId': 'NEW_COMMAND',
        'parameter': parameter,
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      return responseData['result'] == 'success';
    }
    return false;
  } catch (e) {
    print('خطأ في الوظيفة الجديدة: $e');
    return false;
  }
}
```

### 3. إضافة وظيفة في Provider
```dart
// في RouterProvider
Future<bool> callNewFunction(String parameter) async {
  _setLoading(true);
  try {
    bool success = await _apiService.newApiFunction(parameter);
    if (success) {
      // تحديث البيانات المحلية
      notifyListeners();
    } else {
      _setError('فشل في تنفيذ الوظيفة الجديدة');
    }
    return success;
  } catch (e) {
    _setError('خطأ في تنفيذ الوظيفة الجديدة');
    return false;
  } finally {
    _setLoading(false);
  }
}
```

### 4. إضافة ويدجت جديد
```dart
// lib/widgets/new_widget.dart
class NewWidget extends StatelessWidget {
  const NewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<RouterProvider>(
      builder: (context, provider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // محتوى الويدجت
              ],
            ),
          ),
        );
      },
    );
  }
}
```

## معالجة الأخطاء

### 1. أخطاء الشبكة
```dart
try {
  // استدعاء API
} on SocketException {
  _setError('لا يمكن الاتصال بالراوتر');
} on TimeoutException {
  _setError('انتهت مهلة الاتصال');
} on FormatException {
  _setError('خطأ في تنسيق البيانات');
} catch (e) {
  _setError('حدث خطأ غير متوقع');
}
```

### 2. التحقق من صحة البيانات
```dart
// استخدام Validators
String? error = Validators.validatePassword(password);
if (error != null) {
  _setError(error);
  return false;
}
```

## الاختبار

### 1. اختبارات الوحدة
```dart
// test/unit_test.dart
void main() {
  group('RouterProvider Tests', () {
    test('should initialize with correct default values', () {
      final provider = RouterProvider();
      expect(provider.isLoggedIn, false);
      expect(provider.isLoading, false);
    });
  });
}
```

### 2. اختبارات الويدجت
```dart
// test/widget_test.dart
testWidgets('Login screen should display correctly', (tester) async {
  await tester.pumpWidget(MyApp());
  expect(find.text('تسجيل الدخول'), findsOneWidget);
});
```

## التحسين والأداء

### 1. تحسين الشبكة
- استخدام timeout مناسب للطلبات
- إعادة المحاولة عند فشل الطلبات
- تخزين البيانات مؤقتاً

### 2. تحسين الذاكرة
- التخلص من Controllers في dispose()
- استخدام const constructors
- تجنب إعادة بناء الويدجت غير الضرورية

### 3. تحسين واجهة المستخدم
- استخدام ListView.builder للقوائم الطويلة
- تحميل البيانات بشكل تدريجي
- إظهار مؤشرات التحميل

## الأمان

### 1. تشفير كلمات المرور
```dart
String encodedPassword = base64Encode(utf8.encode(password));
```

### 2. التحقق من صحة المدخلات
```dart
if (!Validators.validateSSID(ssid)) {
  return 'اسم الشبكة غير صحيح';
}
```

### 3. معالجة الجلسات
- تسجيل خروج تلقائي عند انتهاء الجلسة
- تنظيف البيانات الحساسة

## النشر

### 1. Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### 2. Web
```bash
flutter build web --release
```

### 3. Windows
```bash
flutter build windows --release
```

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. كتابة الاختبارات
4. التأكد من مرور جميع الاختبارات
5. إرسال Pull Request

## الموارد المفيدة

- [Flutter Documentation](https://flutter.dev/docs)
- [Provider Package](https://pub.dev/packages/provider)
- [HTTP Package](https://pub.dev/packages/http)
- [Material Design](https://material.io/design)
