class Validators {
  // التحقق من كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (value.length < 3) {
      return 'كلمة المرور قصيرة جداً';
    }
    return null;
  }

  // التحقق من اسم الشبكة (SSID)
  static String? validateSSID(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال اسم الشبكة';
    }
    if (value.isEmpty) {
      return 'اسم الشبكة قصير جداً';
    }
    if (value.length > 32) {
      return 'اسم الشبكة طويل جداً (الحد الأقصى 32 حرف)';
    }
    // التحقق من الأحرف المسموحة
    if (!RegExp(r'^[a-zA-Z0-9\s\-_]+$').hasMatch(value)) {
      return 'اسم الشبكة يحتوي على أحرف غير مسموحة';
    }
    return null;
  }

  // التحقق من كلمة مرور الواي فاي
  static String? validateWiFiPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (value.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    if (value.length > 63) {
      return 'كلمة المرور طويلة جداً (الحد الأقصى 63 حرف)';
    }
    return null;
  }

  // التحقق من سرعة الإنترنت
  static String? validateBandwidth(String? value) {
    if (value == null || value.isEmpty) {
      return null; // السرعة غير المحدودة مسموحة
    }
    
    final speed = int.tryParse(value);
    if (speed == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (speed < 0) {
      return 'السرعة لا يمكن أن تكون سالبة';
    }
    
    if (speed > 100000) {
      return 'السرعة عالية جداً (الحد الأقصى 100000 كيلوبت/ث)';
    }
    
    return null;
  }

  // التحقق من عنوان IP
  static String? validateIPAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال عنوان IP';
    }
    
    final ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    );
    
    if (!ipRegex.hasMatch(value)) {
      return 'عنوان IP غير صحيح';
    }
    
    return null;
  }

  // التحقق من عنوان MAC
  static String? validateMACAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال عنوان MAC';
    }
    
    final macRegex = RegExp(
      r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    );
    
    if (!macRegex.hasMatch(value)) {
      return 'عنوان MAC غير صحيح';
    }
    
    return null;
  }

  // التحقق من رقم المنفذ
  static String? validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم المنفذ';
    }
    
    final port = int.tryParse(value);
    if (port == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (port < 1 || port > 65535) {
      return 'رقم المنفذ يجب أن يكون بين 1 و 65535';
    }
    
    return null;
  }

  // التحقق من اسم الجهاز
  static String? validateDeviceName(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال اسم الجهاز';
    }
    
    if (value.isEmpty) {
      return 'اسم الجهاز قصير جداً';
    }
    
    if (value.length > 50) {
      return 'اسم الجهاز طويل جداً (الحد الأقصى 50 حرف)';
    }
    
    return null;
  }

  // التحقق من صحة URL
  static String? validateURL(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الرابط';
    }
    
    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return 'الرابط يجب أن يبدأ بـ http أو https';
      }
      return null;
    } catch (e) {
      return 'الرابط غير صحيح';
    }
  }

  // التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    );
    
    if (!emailRegex.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  // التحقق من صحة رقم الهاتف
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }
    
    // إزالة المسافات والرموز
    final cleanNumber = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (cleanNumber.length < 10 || cleanNumber.length > 15) {
      return 'رقم الهاتف غير صحيح';
    }
    
    if (!RegExp(r'^[\+]?[0-9]+$').hasMatch(cleanNumber)) {
      return 'رقم الهاتف يحتوي على أحرف غير صحيحة';
    }
    
    return null;
  }

  // التحقق من صحة التاريخ
  static String? validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال التاريخ';
    }
    
    try {
      DateTime.parse(value);
      return null;
    } catch (e) {
      return 'التاريخ غير صحيح';
    }
  }

  // التحقق من صحة الوقت
  static String? validateTime(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الوقت';
    }
    
    final timeRegex = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
    
    if (!timeRegex.hasMatch(value)) {
      return 'الوقت غير صحيح (استخدم صيغة HH:MM)';
    }
    
    return null;
  }
}
