import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/permissions_manager.dart';

class PermissionsScreen extends StatefulWidget {
  const PermissionsScreen({super.key});

  @override
  State<PermissionsScreen> createState() => _PermissionsScreenState();
}

class _PermissionsScreenState extends State<PermissionsScreen> {
  bool _isChecking = false;
  Map<String, dynamic>? _permissionsInfo;
  String? _permissionsReport;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأذونات والنظام'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // شرح الأذونات
            Card(
              color: Colors.indigo[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.security, color: Colors.indigo[600]),
                        const SizedBox(width: 8),
                        Text(
                          'فحص الأذونات والنظام',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.indigo[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الشاشة تعرض:\n'
                      '• حالة جميع الأذونات المطلوبة\n'
                      '• إصدار Android ودعم Android 15\n'
                      '• توصيات لتحسين الأداء\n'
                      '• تقرير مفصل للتشخيص',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // زر فحص الأذونات
            ElevatedButton.icon(
              onPressed: _isChecking ? null : _checkPermissions,
              icon:
                  _isChecking
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.refresh),
              label: Text(
                _isChecking ? 'جاري فحص الأذونات...' : 'فحص الأذونات',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 16),

            // النتائج
            if (_permissionsInfo != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتائج فحص الأذونات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _copyReport,
                                  icon: const Icon(Icons.copy),
                                  tooltip: 'نسخ التقرير',
                                ),
                                IconButton(
                                  onPressed: _requestPermissions,
                                  icon: const Icon(Icons.settings),
                                  tooltip: 'طلب الأذونات',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildPermissionsResults(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else if (!_isChecking) ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.security, size: 64, color: Colors.indigo[300]),
                      const SizedBox(height: 16),
                      Text(
                        'اضغط "فحص الأذونات" لبدء الفحص',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      const Text('جاري فحص الأذونات...'),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار، جاري فحص جميع الأذونات\nومعلومات النظام',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _checkPermissions() async {
    setState(() {
      _isChecking = true;
      _permissionsInfo = null;
      _permissionsReport = null;
    });

    try {
      final info = await PermissionsManager.getPermissionsInfo();
      final report = PermissionsManager.formatPermissionsReport(info);

      setState(() {
        _permissionsInfo = info;
        _permissionsReport = report;
        _isChecking = false;
      });
    } catch (e) {
      setState(() {
        _isChecking = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فحص الأذونات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildPermissionsResults() {
    if (_permissionsInfo == null) return const SizedBox();

    List<Widget> widgets = [];

    // معلومات النظام
    widgets.add(
      Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.phone_android, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات النظام',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('إصدار Android: API ${_permissionsInfo!['android_version']}'),
            Text(
              'دعم Android 15: ${_permissionsInfo!['android_15_supported'] ? 'نعم' : 'لا'}',
            ),
          ],
        ),
      ),
    );

    widgets.add(const SizedBox(height: 16));

    // حالة الأذونات
    widgets.add(
      const Text(
        'حالة الأذونات:',
        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
    );
    widgets.add(const SizedBox(height: 8));

    Map<String, bool> permissions = _permissionsInfo!['permissions'];
    permissions.forEach((permission, granted) {
      widgets.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: granted ? Colors.green[50] : Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: granted ? Colors.green[200]! : Colors.red[200]!,
            ),
          ),
          child: Row(
            children: [
              Icon(
                granted ? Icons.check_circle : Icons.error,
                color: granted ? Colors.green[600] : Colors.red[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getArabicPermissionName(permission),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              Text(
                granted ? 'مفعل' : 'غير مفعل',
                style: TextStyle(
                  color: granted ? Colors.green[600] : Colors.red[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    });

    widgets.add(const SizedBox(height: 16));

    // التوصيات
    if (_permissionsInfo!['android_15_supported']) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'جهازك يدعم جميع ميزات Android 15!',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'بعض الميزات المتقدمة قد لا تعمل على إصدار Android القديم',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _copyReport() {
    if (_permissionsReport != null) {
      Clipboard.setData(ClipboardData(text: _permissionsReport!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ تقرير الأذونات إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _requestPermissions() async {
    try {
      bool success = await PermissionsManager.requestAllPermissions(context);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم طلب الأذونات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        // إعادة فحص الأذونات
        _checkPermissions();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في طلب الأذونات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // ترجمة أسماء الأذونات للعربية
  String _getArabicPermissionName(String permission) {
    switch (permission) {
      case 'internet':
        return 'الإنترنت';
      case 'network_state':
        return 'حالة الشبكة';
      case 'wifi_state':
        return 'حالة الواي فاي';
      case 'location':
        return 'الموقع';
      case 'notifications':
        return 'الإشعارات';
      default:
        return permission;
    }
  }
}
