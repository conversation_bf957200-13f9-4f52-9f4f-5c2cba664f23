import 'lib/utils/specialized_login.dart';

void main() async {
  print('🎯 اختبار تسجيل الدخول لراوترك المحدد');
  print('🌐 العنوان: http://192.168.0.1');
  print('📱 الصفحات: #home, #login');
  print('=' * 50);
  print();

  // ضع كلمة مرورك هنا
  String password = 'Hd99884@';
  
  try {
    // تسجيل الدخول المخصص
    Map<String, dynamic> result = await SpecializedLogin.loginToYourRouter(password);
    
    // طباعة التقرير
    String report = SpecializedLogin.formatReport(result);
    print(report);
    
    // النتيجة النهائية
    if (result['success']) {
      print('🎉 تم تسجيل الدخول بنجاح!');
      print('✅ يمكنك الآن استخدام التطبيق');
      
      // معلومات الطريقة الناجحة
      if (result['response_data'] != null) {
        var responseData = result['response_data'];
        print();
        print('📋 تفاصيل الطريقة الناجحة:');
        print('🔧 الطريقة: ${responseData['method']}');
        print('📊 Status Code: ${responseData['status_code']}');
        print('📄 الاستجابة: ${responseData['body']}');
      }
      
    } else {
      print('❌ فشل تسجيل الدخول');
      print();
      print('💡 الحلول المقترحة:');
      print('   1. تحقق من كلمة المرور: $password');
      print('   2. تأكد من اتصالك بشبكة الراوتر');
      print('   3. جرب فتح http://192.168.0.1/index.html#login في المتصفح');
      print('   4. جرب إعادة تشغيل الراوتر');
      print('   5. تحقق من أن الراوتر لا يحتاج captcha أو رمز إضافي');
    }
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
    print();
    print('🔧 تحقق من:');
    print('   - اتصالك بالإنترنت');
    print('   - اتصالك بشبكة الراوتر');
    print('   - أن الراوتر يعمل بشكل طبيعي');
  }
}

// دالة مساعدة لاختبار الاتصال الأساسي
Future<void> testBasicConnection() async {
  print('🔍 اختبار الاتصال الأساسي...');
  
  try {
    // اختبار الصفحة الرئيسية
    print('📄 اختبار http://192.168.0.1/index.html#home');
    // يمكن إضافة كود اختبار هنا
    
    // اختبار صفحة تسجيل الدخول
    print('🔐 اختبار http://192.168.0.1/index.html#login');
    // يمكن إضافة كود اختبار هنا
    
    print('✅ الاختبار الأساسي مكتمل');
    
  } catch (e) {
    print('❌ خطأ في الاختبار الأساسي: $e');
  }
}
