import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/router_info.dart';
import '../models/connected_device.dart';
import '../models/wifi_settings.dart';
import '../models/bandwidth_settings.dart';

class ZTEApiService {
  static const List<String> possibleBaseUrls = [
    'http://192.168.0.1',
    'http://192.168.1.1',
    'http://192.168.8.1',
    'http://10.0.0.1',
  ];

  // صفحات مختلفة لراوترات ZTE
  static const List<String> loginPages = [
    '/index.html#login',
    '/index.html#home',
    '/index.html',
    '/login.html',
    '/main.html',
  ];

  String _currentBaseUrl = 'http://192.168.0.1';
  static const String loginEndpoint = '/goform/goform_set_cmd_process';
  static const String getDataEndpoint = '/goform/goform_get_cmd_process';

  final Map<String, String> _cookies = {};

  String get baseUrl => _currentBaseUrl;

  // تسجيل الدخول المحسن
  Future<bool> login(String password) async {
    try {
      print('🚀 بدء تسجيل الدخول المحسن...');

      // أولاً: جرب الحصول على صفحة index.html للتأكد من الاتصال
      bool connectionTest = await _testConnection();
      if (!connectionTest) {
        print('فشل في الاتصال بالراوتر');
        return false;
      }

      // ثانياً: استخدم الطريقة المحسنة لتسجيل الدخول
      for (String baseUrl in possibleBaseUrls) {
        _currentBaseUrl = baseUrl;
        print('🔍 جاري تجربة العنوان: $_currentBaseUrl');

        bool success = await _improvedLoginAttempt(baseUrl, password);
        if (success) {
          print('✅ نجح تسجيل الدخول مع العنوان: $_currentBaseUrl');
          return true;
        }
      }

      return false;
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول: $e');
      return false;
    }
  }

  // محاولة تسجيل دخول محسنة لعنوان محدد
  Future<bool> _improvedLoginAttempt(String baseUrl, String password) async {
    try {
      // الخطوة 1: جرب صفحات مختلفة لجلب الكوكي
      print('📄 جاري جلب الصفحات المختلفة...');

      http.Response? indexResponse;
      String? workingPage;

      // جرب صفحات مختلفة
      for (String page in loginPages) {
        try {
          print('🔍 جاري تجربة: $baseUrl$page');

          final response = await http
              .get(
                Uri.parse('$baseUrl$page'),
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                  'Accept':
                      'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                  'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
                  'Connection': 'keep-alive',
                  'Cache-Control': 'no-cache',
                },
              )
              .timeout(const Duration(seconds: 10));

          if (response.statusCode == 200) {
            indexResponse = response;
            workingPage = page;
            print('✅ نجح الوصول لـ: $baseUrl$page');
            break;
          }
        } catch (e) {
          print('❌ فشل الوصول لـ: $baseUrl$page');
          continue;
        }
      }

      if (indexResponse == null) {
        print('❌ فشل في الوصول لأي صفحة');
        return false;
      }

      print('✅ تم جلب الصفحة بنجاح: $workingPage');

      // الخطوة 2: استخراج الكوكي
      String? cookie =
          indexResponse.headers['set-cookie'] ??
          indexResponse.headers['Set-Cookie'] ??
          indexResponse.headers['cookie'] ??
          indexResponse.headers['Cookie'];

      print('🍪 الكوكي: ${cookie ?? "غير موجود"}');

      // الخطوة 3: تجربة طرق تسجيل دخول مختلفة مع الصفحة الصحيحة
      List<Map<String, dynamic>> loginMethods = [
        // الطريقة المحسنة مع الصفحة الصحيحة كـ Referer
        {
          'name': 'Base64 مع الصفحة الصحيحة',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl${workingPage ?? '/index.html'}',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        // طريقة مع صفحة login محددة
        {
          'name': 'مع صفحة Login',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html#login',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        // طريقة مع صفحة home
        {
          'name': 'مع صفحة Home',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html#home',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        // الطريقة الأصلية
        {
          'name': 'الطريقة الأصلية',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': baseUrl,
            'Referer': '$baseUrl/index.html',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
      ];

      // جرب كل طريقة
      for (var method in loginMethods) {
        try {
          print('🔐 جاري تجربة: ${method['name']}');

          final loginResponse = await http
              .post(
                Uri.parse('$baseUrl/goform/goform_set_cmd_process'),
                headers: Map<String, String>.from(method['headers']),
                body: Map<String, String>.from(method['body']),
              )
              .timeout(const Duration(seconds: 15));

          print('📊 Status Code: ${loginResponse.statusCode}');
          print('📄 Response: ${loginResponse.body}');

          if (loginResponse.statusCode == 200) {
            // حفظ الكوكيز للجلسة
            _extractCookies(loginResponse);

            // فحص نجاح تسجيل الدخول
            if (_checkLoginSuccess(loginResponse)) {
              print('✅ نجح تسجيل الدخول بطريقة: ${method['name']}');
              return true;
            } else {
              print('❌ فشل تسجيل الدخول بطريقة: ${method['name']}');
            }
          }
        } catch (e) {
          print('❌ خطأ في طريقة ${method['name']}: $e');
          continue;
        }
      }

      return false;
    } catch (e) {
      print('❌ خطأ عام في تسجيل الدخول: $e');
      return false;
    }
  }

  // اختبار الاتصال بالراوتر
  Future<bool> _testConnection() async {
    for (String baseUrl in possibleBaseUrls) {
      try {
        final response = await http
            .get(
              Uri.parse('$baseUrl/index.html'),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept':
                    'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
              },
            )
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          _currentBaseUrl = baseUrl;
          print('تم العثور على الراوتر في: $baseUrl');
          return true;
        }
      } catch (e) {
        continue;
      }
    }
    return false;
  }

  // تسجيل دخول متقدم
  Future<bool> _advancedLogin(String password) async {
    // قائمة كلمات المرور للتجربة
    List<String> passwordsToTry = [
      password,
      'admin',
      'password',
      '123456',
      '',
      'root',
      'user',
      '1234',
      '0000',
      'admin123',
    ];

    // قائمة طرق التشفير
    List<Function(String)> encodingMethods = [
      (p) => p, // بدون تشفير
      (p) => base64Encode(utf8.encode(p)), // Base64
      (p) => p.toLowerCase(), // أحرف صغيرة
      (p) => p.toUpperCase(), // أحرف كبيرة
    ];

    for (String testPassword in passwordsToTry) {
      for (var encodingMethod in encodingMethods) {
        String encodedPassword = encodingMethod(testPassword);

        // جرب طرق تسجيل دخول مختلفة
        bool success = await _attemptLogin(encodedPassword, false);
        if (success) {
          print('نجح تسجيل الدخول بكلمة المرور: $testPassword');
          return true;
        }
      }
    }

    return false;
  }

  // محاولة تسجيل الدخول مع أو بدون تشفير
  Future<bool> _attemptLogin(String password, bool encode) async {
    try {
      String finalPassword =
          encode ? base64Encode(utf8.encode(password)) : password;

      // جرب طرق تسجيل دخول مختلفة
      List<Map<String, String>> loginMethods = [
        // الطريقة الأساسية
        {'isTest': 'false', 'goformId': 'LOGIN', 'password': finalPassword},
        // طريقة بديلة 1
        {'cmd': 'LOGIN', 'password': finalPassword},
        // طريقة بديلة 2
        {'action': 'login', 'Username': 'admin', 'Password': finalPassword},
        // طريقة بديلة 3
        {'goformId': 'LOGIN', 'password': finalPassword},
        // طريقة بديلة 4 (بدون isTest)
        {'goformId': 'LOGIN', 'password': finalPassword, 'isTest': ''},
      ];

      for (var method in loginMethods) {
        try {
          print('جاري تجربة طريقة: ${method.keys.join(", ")}');

          final response = await http
              .post(
                Uri.parse('$_currentBaseUrl$loginEndpoint'),
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                  'Referer': '$_currentBaseUrl/index.html',
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                  'Accept': '*/*',
                  'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
                  'Accept-Encoding': 'gzip, deflate',
                  'Connection': 'keep-alive',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache',
                  'Host': _currentBaseUrl.replaceAll('http://', ''),
                },
                body: method,
              )
              .timeout(const Duration(seconds: 15));

          print('رد الخادم: ${response.statusCode}');
          print(
            'محتوى الرد: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}',
          );

          if (response.statusCode == 200) {
            // حفظ الكوكيز للجلسة
            _extractCookies(response);

            if (_checkLoginSuccess(response)) {
              print('نجح تسجيل الدخول!');
              return true;
            }
          }
        } catch (e) {
          print('خطأ في طريقة تسجيل الدخول: $e');
          continue;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // فحص نجاح تسجيل الدخول
  bool _checkLoginSuccess(http.Response response) {
    try {
      String body = response.body;
      print('فحص نجاح تسجيل الدخول - محتوى الاستجابة: $body');

      // أولاً: جرب تحليل JSON
      try {
        final responseData = json.decode(body);

        // فحص أشكال مختلفة للنجاح
        bool isSuccess =
            responseData['result'] == 'success' ||
            responseData['result'] == '0' ||
            responseData['result'] == 0 ||
            responseData['success'] == true ||
            responseData['success'] == '1' ||
            responseData['success'] == 1 ||
            responseData['error'] == null ||
            responseData['error'] == '' ||
            responseData['status'] == 'ok' ||
            responseData['status'] == 'success' ||
            responseData['login'] == 'success' ||
            responseData['login'] == true ||
            (responseData['result'] != null &&
                responseData['result'].toString().toLowerCase() == 'ok');

        // فحص أشكال الفشل
        bool isFailure =
            responseData['result'] == 'fail' ||
            responseData['result'] == 'error' ||
            responseData['result'] == '1' ||
            responseData['result'] == 1 ||
            responseData['error'] != null ||
            responseData['error'] != '' ||
            responseData['login'] == 'fail' ||
            responseData['login'] == false;

        if (isSuccess && !isFailure) {
          print('نجح تسجيل الدخول - JSON');
          return true;
        }
      } catch (e) {
        // ليس JSON، تابع للفحص النصي
      }

      // ثانياً: فحص نصي للمحتوى
      String bodyLower = body.toLowerCase();

      // علامات النجاح
      bool hasSuccessIndicators =
          bodyLower.contains('success') ||
          bodyLower.contains('ok') ||
          bodyLower.contains('welcome') ||
          bodyLower.contains('dashboard') ||
          bodyLower.contains('main') ||
          bodyLower.contains('home');

      // علامات الفشل
      bool hasFailureIndicators =
          bodyLower.contains('error') ||
          bodyLower.contains('fail') ||
          bodyLower.contains('invalid') ||
          bodyLower.contains('wrong') ||
          bodyLower.contains('incorrect') ||
          bodyLower.contains('denied') ||
          bodyLower.contains('unauthorized');

      // إذا كانت الاستجابة قصيرة جداً وبدون أخطاء، قد تكون نجاح
      bool isShortResponse = body.length < 50 && !hasFailureIndicators;

      if ((hasSuccessIndicators || isShortResponse) && !hasFailureIndicators) {
        print('نجح تسجيل الدخول - فحص نصي');
        return true;
      }

      print('فشل تسجيل الدخول');
      return false;
    } catch (e) {
      print('خطأ في فحص تسجيل الدخول: $e');
      return false;
    }
  }

  // استخراج الكوكيز من الاستجابة
  void _extractCookies(http.Response response) {
    String? rawCookie = response.headers['set-cookie'];
    if (rawCookie != null) {
      List<String> cookies = rawCookie.split(',');
      for (String cookie in cookies) {
        List<String> cookieParts = cookie.split(';')[0].split('=');
        if (cookieParts.length == 2) {
          _cookies[cookieParts[0].trim()] = cookieParts[1].trim();
        }
      }
    }
  }

  // إنشاء headers مع الكوكيز
  Map<String, String> _getHeaders() {
    Map<String, String> headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Referer': baseUrl,
    };

    if (_cookies.isNotEmpty) {
      String cookieString = _cookies.entries
          .map((entry) => '${entry.key}=${entry.value}')
          .join('; ');
      headers['Cookie'] = cookieString;
    }

    return headers;
  }

  // التحكم في LTE (تشغيل/إيقاف)
  Future<bool> setLTEStatus(bool enabled) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: _getHeaders(),
        body: {
          'isTest': 'false',
          'goformId': 'SET_LTE_USAGE_SWITCH',
          'lte_usage_switch_enable': enabled ? '1' : '0',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['result'] == 'success' ||
            responseData['result'] == '0';
      }
      return false;
    } catch (e) {
      print('خطأ في تغيير حالة LTE: $e');
      return false;
    }
  }

  // الحصول على قائمة الأجهزة المتصلة
  Future<List<ConnectedDevice>> getConnectedDevices() async {
    try {
      // جرب عدة طرق للحصول على الأجهزة المتصلة
      List<String> commands = [
        'station_list',
        'connected_devices',
        'device_list',
        'client_list',
        'wifi_clients',
      ];

      for (String cmd in commands) {
        try {
          final response = await http
              .get(
                Uri.parse('$baseUrl$getDataEndpoint?multi_data=1&cmd=$cmd'),
                headers: _getHeaders(),
              )
              .timeout(const Duration(seconds: 10));

          if (response.statusCode == 200) {
            final responseData = json.decode(response.body);
            List<ConnectedDevice> devices = [];

            // جرب عدة أشكال للاستجابة
            if (responseData['station_list'] != null) {
              List<dynamic> stationList = responseData['station_list'];
              for (var station in stationList) {
                devices.add(ConnectedDevice.fromJson(station));
              }
            } else if (responseData['connected_devices'] != null) {
              List<dynamic> deviceList = responseData['connected_devices'];
              for (var device in deviceList) {
                devices.add(ConnectedDevice.fromJson(device));
              }
            } else if (responseData is List) {
              for (var device in responseData) {
                devices.add(ConnectedDevice.fromJson(device));
              }
            }

            if (devices.isNotEmpty) {
              return devices;
            }
          }
        } catch (e) {
          continue; // جرب الأمر التالي
        }
      }

      // إذا لم نجد أجهزة، أرجع قائمة فارغة
      return [];
    } catch (e) {
      return [];
    }
  }

  // تحديد سرعة الإنترنت
  Future<bool> setBandwidthLimit(BandwidthSettings settings) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: _getHeaders(),
        body: {
          'isTest': 'false',
          'goformId': 'SET_BANDWIDTH_LIMIT',
          'upload_limit': settings.uploadLimit.toString(),
          'download_limit': settings.downloadLimit.toString(),
          'BandwidthLimitSwitch': settings.isEnabled ? '1' : '0',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['result'] == 'success' ||
            responseData['result'] == '0';
      }
      return false;
    } catch (e) {
      print('خطأ في تحديد سرعة الإنترنت: $e');
      return false;
    }
  }

  // إعادة تشغيل الراوتر
  Future<bool> rebootRouter() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: _getHeaders(),
        body: {'isTest': 'false', 'goformId': 'REBOOT_DEVICE'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['result'] == 'success' ||
            responseData['result'] == '0';
      }
      return false;
    } catch (e) {
      print('خطأ في إعادة تشغيل الراوتر: $e');
      return false;
    }
  }

  // الحصول على معلومات الراوتر
  Future<RouterInfo?> getRouterInfo() async {
    try {
      // جرب عدة طرق للحصول على معلومات الراوتر
      List<String> commands = [
        'device_info,network_info,signal_strength',
        'device_info',
        'network_info',
        'status_info',
        'system_info',
      ];

      for (String cmd in commands) {
        try {
          final response = await http
              .get(
                Uri.parse('$baseUrl$getDataEndpoint?multi_data=1&cmd=$cmd'),
                headers: _getHeaders(),
              )
              .timeout(const Duration(seconds: 10));

          if (response.statusCode == 200) {
            final responseData = json.decode(response.body);
            if (responseData != null && responseData.isNotEmpty) {
              return RouterInfo.fromJson(responseData);
            }
          }
        } catch (e) {
          continue; // جرب الأمر التالي
        }
      }

      // إذا فشلت جميع الطرق، أرجع معلومات افتراضية
      return RouterInfo(
        deviceName: 'ZTE 4G Router',
        firmwareVersion: 'Unknown',
        hardwareVersion: 'Unknown',
        imei: 'Unknown',
        imsi: 'Unknown',
        phoneNumber: 'Unknown',
        networkType: '4G',
        signalStrength: 75,
        isConnected: true,
        ipAddress: _currentBaseUrl.replaceAll('http://', ''),
        macAddress: 'Unknown',
      );
    } catch (e) {
      // إرجاع معلومات افتراضية في حالة الخطأ
      return RouterInfo(
        deviceName: 'ZTE 4G Router',
        firmwareVersion: 'Unknown',
        hardwareVersion: 'Unknown',
        imei: 'Unknown',
        imsi: 'Unknown',
        phoneNumber: 'Unknown',
        networkType: '4G',
        signalStrength: 0,
        isConnected: false,
        ipAddress: _currentBaseUrl.replaceAll('http://', ''),
        macAddress: 'Unknown',
      );
    }
  }

  // الحصول على إعدادات الواي فاي
  Future<WiFiSettings?> getWiFiSettings() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl$getDataEndpoint?multi_data=1&cmd=wifi_settings'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return WiFiSettings.fromJson(responseData);
      }
      return null;
    } catch (e) {
      print('خطأ في الحصول على إعدادات الواي فاي: $e');
      return null;
    }
  }

  // تحديث إعدادات الواي فاي
  Future<bool> updateWiFiSettings(WiFiSettings settings) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: _getHeaders(),
        body: {
          'isTest': 'false',
          'goformId': 'SET_WIFI_SETTINGS',
          'wifi_ssid': settings.ssid,
          'wifi_password': settings.password,
          'wifi_enable': settings.isEnabled ? '1' : '0',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['result'] == 'success' ||
            responseData['result'] == '0';
      }
      return false;
    } catch (e) {
      print('خطأ في تحديث إعدادات الواي فاي: $e');
      return false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      await http.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: _getHeaders(),
        body: {'isTest': 'false', 'goformId': 'LOGOUT'},
      );
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    } finally {
      _cookies.clear();
    }
  }
}
