# دليل البناء والنشر - ZTE Router Control

## بناء التطبيق

### 1. متطلبات البناء
- Flutter SDK 3.7.2 أو أحدث
- Android SDK (للأندرويد)
- Xcode (لـ iOS - على macOS فقط)
- Visual Studio (لـ Windows)

### 2. بناء التطبيق للمنصات المختلفة

#### Android APK
```bash
# بناء APK للتطوير
flutter build apk --debug

# بناء APK للإنتاج
flutter build apk --release

# بناء APK مقسم حسب المعمارية (أصغر حجماً)
flutter build apk --split-per-abi --release
```

#### Android App Bundle (للنشر في Google Play)
```bash
flutter build appbundle --release
```

#### iOS
```bash
# بناء للمحاكي
flutter build ios --debug --simulator

# بناء للجهاز
flutter build ios --release
```

#### Web
```bash
# بناء للويب
flutter build web --release

# بناء مع تحسينات إضافية
flutter build web --release --web-renderer html
```

#### Windows
```bash
flutter build windows --release
```

#### macOS
```bash
flutter build macos --release
```

#### Linux
```bash
flutter build linux --release
```

## مواقع الملفات المبنية

### Android
- **APK**: `build/app/outputs/flutter-apk/app-release.apk`
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab`

### iOS
- **IPA**: `build/ios/ipa/`

### Web
- **ملفات الويب**: `build/web/`

### Desktop
- **Windows**: `build/windows/x64/runner/Release/`
- **macOS**: `build/macos/Build/Products/Release/`
- **Linux**: `build/linux/x64/release/bundle/`

## إعدادات التطبيق

### تغيير اسم التطبيق
1. **Android**: عدّل `android/app/src/main/AndroidManifest.xml`
```xml
<application android:label="ZTE Router Control">
```

2. **iOS**: عدّل `ios/Runner/Info.plist`
```xml
<key>CFBundleDisplayName</key>
<string>ZTE Router Control</string>
```

3. **Web**: عدّل `web/index.html`
```html
<title>ZTE Router Control</title>
```

### تغيير أيقونة التطبيق
1. ضع الأيقونة في `assets/icon/app_icon.png` (1024x1024)
2. أضف `flutter_launcher_icons` للمشروع:
```yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icon/app_icon.png"
```
3. شغّل: `flutter pub run flutter_launcher_icons:main`

### تغيير معرف التطبيق (Package Name)
1. **Android**: عدّل `android/app/build.gradle`
```gradle
android {
    defaultConfig {
        applicationId "com.yourcompany.zte_router_control"
    }
}
```

2. **iOS**: عدّل في Xcode أو `ios/Runner.xcodeproj`

## التوقيع والأمان

### Android
1. **إنشاء مفتاح التوقيع**:
```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

2. **إعداد التوقيع**: أنشئ `android/key.properties`
```properties
storePassword=<password>
keyPassword=<password>
keyAlias=upload
storeFile=<path-to-keystore>
```

3. **تحديث build.gradle**: عدّل `android/app/build.gradle`

### iOS
- استخدم Xcode لإعداد التوقيع والشهادات
- تأكد من إعداد Provisioning Profile

## النشر

### Google Play Store
1. بناء App Bundle: `flutter build appbundle --release`
2. رفع الملف في Google Play Console
3. ملء معلومات التطبيق والوصف
4. إعداد الأسعار والتوزيع
5. مراجعة ونشر

### Apple App Store
1. بناء للإنتاج: `flutter build ios --release`
2. فتح Xcode وأرشفة التطبيق
3. رفع عبر App Store Connect
4. ملء معلومات التطبيق
5. إرسال للمراجعة

### Microsoft Store
1. بناء للويندوز: `flutter build windows --release`
2. إنشاء حزمة MSIX
3. رفع في Partner Center

### الويب
1. بناء للويب: `flutter build web --release`
2. رفع ملفات `build/web/` لخادم الويب
3. إعداد HTTPS والدومين

## تحسين الأداء

### تقليل حجم التطبيق
```bash
# بناء مع تحسينات الحجم
flutter build apk --release --shrink

# تقسيم APK حسب المعمارية
flutter build apk --split-per-abi --release
```

### تحسين الويب
```bash
# بناء مع تحسينات الويب
flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_USE_SKIA=true
```

## اختبار البناء

### اختبار APK
```bash
# تثبيت على جهاز متصل
flutter install --release

# أو تثبيت APK مباشرة
adb install build/app/outputs/flutter-apk/app-release.apk
```

### اختبار الويب
```bash
# تشغيل خادم محلي
cd build/web
python -m http.server 8000
# ثم افتح http://localhost:8000
```

## استكشاف الأخطاء

### مشاكل البناء الشائعة
1. **خطأ Gradle**: تحديث Android Gradle Plugin
2. **مشاكل التوقيع**: تحقق من إعدادات key.properties
3. **مساحة القرص**: تنظيف build cache: `flutter clean`
4. **إصدار Flutter**: تحديث: `flutter upgrade`

### أوامر مفيدة
```bash
# تنظيف المشروع
flutter clean

# الحصول على التبعيات
flutter pub get

# فحص المشروع
flutter doctor

# عرض الأجهزة المتاحة
flutter devices

# تشغيل الاختبارات
flutter test
```

## معلومات إضافية

### حجم التطبيق الحالي
- **Android APK**: ~21.5 MB
- **Web**: ~2-3 MB (مضغوط)
- **Windows**: ~15-20 MB

### الميزات المدعومة
- ✅ Android 5.0+ (API 21+)
- ✅ iOS 11.0+
- ✅ Web (جميع المتصفحات الحديثة)
- ✅ Windows 10+
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+)

### الأذونات المطلوبة
- **Android**: INTERNET, ACCESS_NETWORK_STATE
- **iOS**: NSAppTransportSecurity (لـ HTTP)
- **Web**: لا توجد أذونات خاصة

---

للمساعدة في البناء أو النشر، راجع الوثائق الرسمية لـ Flutter أو افتح issue في GitHub.
