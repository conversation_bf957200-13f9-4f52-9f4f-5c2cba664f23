import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/advanced_diagnostics.dart';

class AdvancedDebugScreen extends StatefulWidget {
  const AdvancedDebugScreen({super.key});

  @override
  State<AdvancedDebugScreen> createState() => _AdvancedDebugScreenState();
}

class _AdvancedDebugScreenState extends State<AdvancedDebugScreen> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isDebugging = false;
  Map<String, dynamic>? _diagnosisResult;
  String? _detailedReport;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص متقدم'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // شرح الأداة
            Card(
              color: Colors.deepPurple[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.science, color: Colors.deepPurple[600]),
                        const SizedBox(width: 8),
                        Text(
                          'تشخيص متقدم لتسجيل الدخول',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.deepPurple[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الأداة تحاكي المتصفح بالضبط وتقوم بـ:\n'
                      '• فحص الاتصال بجميع الصفحات\n'
                      '• تحليل محتوى صفحة تسجيل الدخول\n'
                      '• محاكاة دقيقة لعملية تسجيل الدخول\n'
                      '• تقرير مفصل مع توصيات محددة',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // إدخال كلمة المرور
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                hintText: 'أدخل كلمة مرور الراوتر',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),

            const SizedBox(height: 16),

            // زر بدء التشخيص المتقدم
            ElevatedButton.icon(
              onPressed: _isDebugging ? null : _startAdvancedDiagnosis,
              icon: _isDebugging 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.science),
              label: Text(_isDebugging ? 'جاري التشخيص المتقدم...' : 'بدء التشخيص المتقدم'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 16),

            // النتائج
            if (_diagnosisResult != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتائج التشخيص المتقدم',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  onPressed: _copyDetailedReport,
                                  icon: const Icon(Icons.copy),
                                  tooltip: 'نسخ التقرير المفصل',
                                ),
                                IconButton(
                                  onPressed: _shareResults,
                                  icon: const Icon(Icons.share),
                                  tooltip: 'مشاركة النتائج',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildAdvancedResults(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else if (!_isDebugging) ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.science,
                        size: 64,
                        color: Colors.deepPurple[300],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'أدخل كلمة المرور واضغط "بدء التشخيص المتقدم"',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'سيستغرق التشخيص 30-60 ثانية\nوسيعطي تقرير مفصل جداً',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      const Text('جاري التشخيص المتقدم...'),
                      const SizedBox(height: 8),
                      Text(
                        'يرجى الانتظار، جاري محاكاة المتصفح بالضبط\nوتجربة جميع الطرق الممكنة',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _startAdvancedDiagnosis() async {
    if (_passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال كلمة المرور أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isDebugging = true;
      _diagnosisResult = null;
      _detailedReport = null;
    });

    try {
      final result = await AdvancedDiagnostics.diagnoseLoginProcess(_passwordController.text);
      final report = AdvancedDiagnostics.formatDetailedReport(result);

      setState(() {
        _diagnosisResult = result;
        _detailedReport = report;
        _isDebugging = false;
      });
    } catch (e) {
      setState(() {
        _isDebugging = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التشخيص المتقدم: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildAdvancedResults() {
    if (_diagnosisResult == null) return const SizedBox();

    List<Widget> widgets = [];

    // النتيجة العامة
    if (_diagnosisResult!['success'] == true) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  const Text(
                    'نجح التشخيص!',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('الطريقة الناجحة: ${_diagnosisResult!['successful_method']}'),
              const SizedBox(height: 4),
              const Text(
                'يمكنك الآن استخدام التطبيق الرئيسي',
                style: TextStyle(color: Colors.green),
              ),
            ],
          ),
        ),
      );
    } else {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'فشل تسجيل الدخول بجميع الطرق',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      );
    }

    widgets.add(const SizedBox(height: 16));

    // التوصيات
    if (_diagnosisResult!['recommendations'] != null) {
      widgets.add(
        const Text(
          'التوصيات المفصلة:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
      widgets.add(const SizedBox(height: 8));

      for (String recommendation in _diagnosisResult!['recommendations']) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(recommendation),
          ),
        );
      }
    }

    widgets.add(const SizedBox(height: 16));

    // معلومات إضافية
    widgets.add(
      ExpansionTile(
        title: const Text('تفاصيل تقنية'),
        children: [
          Text('عدد الخطوات: ${_diagnosisResult!['steps']?.length ?? 0}'),
          Text('عدد الاستجابات: ${_diagnosisResult!['detailed_responses']?.length ?? 0}'),
          Text('الوقت: ${_diagnosisResult!['timestamp']}'),
        ],
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _copyDetailedReport() {
    if (_detailedReport != null) {
      Clipboard.setData(ClipboardData(text: _detailedReport!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ التقرير المفصل إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _shareResults() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استخدم زر النسخ لمشاركة التقرير المفصل'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }
}
