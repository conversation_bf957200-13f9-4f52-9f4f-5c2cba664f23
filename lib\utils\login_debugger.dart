import 'dart:convert';
import 'package:http/http.dart' as http;

class LoginDebugger {
  static const List<String> possibleBaseUrls = [
    'http://192.168.0.1',
    'http://192.168.1.1',
    'http://192.168.8.1',
    'http://10.0.0.1',
  ];

  // تشخيص شامل لمشكلة تسجيل الدخول
  static Future<Map<String, dynamic>> diagnoseLogin(String password) async {
    Map<String, dynamic> diagnosis = {
      'timestamp': DateTime.now().toIso8601String(),
      'password_tested': password,
      'results': {},
      'recommendations': [],
    };

    // 1. اختبار الاتصال بالراوتر
    String? workingUrl = await _testConnections();
    if (workingUrl == null) {
      diagnosis['error'] = 'لا يمكن الوصول لأي راوتر على العناوين المعتادة';
      diagnosis['recommendations'].add('تأكد من اتصالك بشبكة الراوتر');
      diagnosis['recommendations'].add('جرب فتح http://192.168.0.1 في المتصفح');
      return diagnosis;
    }

    diagnosis['working_url'] = workingUrl;
    diagnosis['recommendations'].add('تم العثور على الراوتر في: $workingUrl');

    // 2. اختبار طرق تسجيل الدخول المختلفة
    List<String> passwordsToTest = [
      password,
      'admin',
      'password',
      '123456',
      '',
      'root',
      'user',
    ];

    for (String testPassword in passwordsToTest) {
      Map<String, dynamic> passwordResult = await _testPassword(workingUrl, testPassword);
      diagnosis['results'][testPassword] = passwordResult;
      
      if (passwordResult['success'] == true) {
        diagnosis['successful_password'] = testPassword;
        diagnosis['successful_method'] = passwordResult['successful_method'];
        break;
      }
    }

    // 3. تحليل النتائج وإعطاء توصيات
    _analyzeResults(diagnosis);

    return diagnosis;
  }

  // اختبار الاتصال بالراوترات المختلفة
  static Future<String?> _testConnections() async {
    for (String url in possibleBaseUrls) {
      try {
        final response = await http.get(
          Uri.parse('$url/index.html'),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          },
        ).timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          return url;
        }
      } catch (e) {
        continue;
      }
    }
    return null;
  }

  // اختبار كلمة مرور محددة
  static Future<Map<String, dynamic>> _testPassword(String baseUrl, String password) async {
    Map<String, dynamic> result = {
      'password': password,
      'success': false,
      'attempts': [],
      'successful_method': null,
    };

    // طرق تسجيل الدخول المختلفة
    List<Map<String, dynamic>> loginMethods = [
      {
        'name': 'Base64 Encoded',
        'body': {
          'isTest': 'false',
          'goformId': 'LOGIN',
          'password': base64Encode(utf8.encode(password)),
        }
      },
      {
        'name': 'Plain Text',
        'body': {
          'isTest': 'false',
          'goformId': 'LOGIN',
          'password': password,
        }
      },
      {
        'name': 'With Username',
        'body': {
          'isTest': 'false',
          'goformId': 'LOGIN',
          'username': 'admin',
          'password': base64Encode(utf8.encode(password)),
        }
      },
      {
        'name': 'CMD Method',
        'body': {
          'cmd': 'LOGIN',
          'password': base64Encode(utf8.encode(password)),
        }
      },
      {
        'name': 'Action Method',
        'body': {
          'action': 'login',
          'Username': 'admin',
          'Password': password,
        }
      },
    ];

    for (var method in loginMethods) {
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/goform/goform_set_cmd_process'),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': '$baseUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
          },
          body: method['body'],
        ).timeout(const Duration(seconds: 10));

        Map<String, dynamic> attemptResult = {
          'method': method['name'],
          'status_code': response.statusCode,
          'response_body': response.body,
          'success': _checkSuccess(response.body),
        };

        result['attempts'].add(attemptResult);

        if (attemptResult['success']) {
          result['success'] = true;
          result['successful_method'] = method['name'];
          break;
        }
      } catch (e) {
        result['attempts'].add({
          'method': method['name'],
          'error': e.toString(),
          'success': false,
        });
      }
    }

    return result;
  }

  // فحص نجاح تسجيل الدخول
  static bool _checkSuccess(String responseBody) {
    try {
      final json = jsonDecode(responseBody);
      return json['result'] == 'success' || 
             json['result'] == '0' || 
             json['result'] == 0 ||
             json['success'] == true ||
             json['success'] == '1';
    } catch (e) {
      String body = responseBody.toLowerCase();
      return body.contains('success') && 
             !body.contains('error') && 
             !body.contains('fail');
    }
  }

  // تحليل النتائج وإعطاء توصيات
  static void _analyzeResults(Map<String, dynamic> diagnosis) {
    if (diagnosis.containsKey('successful_password')) {
      diagnosis['recommendations'].add('✅ تم العثور على كلمة المرور الصحيحة: ${diagnosis['successful_password']}');
      diagnosis['recommendations'].add('✅ الطريقة الناجحة: ${diagnosis['successful_method']}');
    } else {
      diagnosis['recommendations'].add('❌ لم تنجح أي من كلمات المرور المجربة');
      diagnosis['recommendations'].add('🔍 جرب كلمات مرور أخرى مثل:');
      diagnosis['recommendations'].add('   - الرقم التسلسلي للراوتر');
      diagnosis['recommendations'].add('   - آخر 4 أرقام من IMEI');
      diagnosis['recommendations'].add('   - تاريخ الميلاد');
      diagnosis['recommendations'].add('   - admin123, password123');
    }

    // تحليل الاستجابات
    Map<String, dynamic> results = diagnosis['results'];
    bool hasAnyResponse = false;
    
    for (var passwordResult in results.values) {
      if (passwordResult['attempts'].isNotEmpty) {
        hasAnyResponse = true;
        break;
      }
    }

    if (!hasAnyResponse) {
      diagnosis['recommendations'].add('⚠️ لا توجد استجابة من الراوتر');
      diagnosis['recommendations'].add('🔧 تحقق من:');
      diagnosis['recommendations'].add('   - اتصال الإنترنت');
      diagnosis['recommendations'].add('   - إعدادات جدار الحماية');
      diagnosis['recommendations'].add('   - إعادة تشغيل الراوتر');
    }
  }

  // طباعة تقرير التشخيص
  static String formatDiagnosisReport(Map<String, dynamic> diagnosis) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🔍 تقرير تشخيص تسجيل الدخول');
    report.writeln('=' * 50);
    report.writeln('⏰ الوقت: ${diagnosis['timestamp']}');
    report.writeln('🔑 كلمة المرور المختبرة: ${diagnosis['password_tested']}');
    
    if (diagnosis.containsKey('working_url')) {
      report.writeln('🌐 عنوان الراوتر: ${diagnosis['working_url']}');
    }
    
    if (diagnosis.containsKey('error')) {
      report.writeln('❌ خطأ: ${diagnosis['error']}');
    }
    
    report.writeln('\n📋 التوصيات:');
    for (String recommendation in diagnosis['recommendations']) {
      report.writeln('  $recommendation');
    }
    
    if (diagnosis['results'].isNotEmpty) {
      report.writeln('\n📊 تفاصيل الاختبارات:');
      Map<String, dynamic> results = diagnosis['results'];
      
      for (var entry in results.entries) {
        String password = entry.key;
        Map<String, dynamic> result = entry.value;
        
        report.writeln('  🔑 كلمة المرور: $password');
        report.writeln('     النتيجة: ${result['success'] ? '✅ نجح' : '❌ فشل'}');
        
        if (result['success']) {
          report.writeln('     الطريقة الناجحة: ${result['successful_method']}');
        }
        
        report.writeln('     عدد المحاولات: ${result['attempts'].length}');
        report.writeln('');
      }
    }
    
    return report.toString();
  }
}
