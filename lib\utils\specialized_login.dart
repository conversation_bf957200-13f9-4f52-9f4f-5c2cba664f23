import 'dart:convert';
import 'package:http/http.dart' as http;

class SpecializedLogin {
  static const String routerUrl = 'http://192.168.0.1';
  
  // تسجيل دخول مخصص لراوترك
  static Future<Map<String, dynamic>> loginToYourRouter(String password) async {
    Map<String, dynamic> result = {
      'success': false,
      'message': '',
      'response_data': null,
      'steps': [],
    };

    try {
      print('🚀 بدء تسجيل الدخول المخصص لراوترك...');
      
      // الخطوة 1: الوصول لصفحة تسجيل الدخول
      result['steps'].add('الوصول لصفحة تسجيل الدخول');
      print('📄 جاري الوصول لصفحة تسجيل الدخول...');
      
      final loginPageResponse = await http.get(
        Uri.parse('$routerUrl/index.html#login'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      ).timeout(const Duration(seconds: 10));

      if (loginPageResponse.statusCode != 200) {
        result['message'] = 'فشل في الوصول لصفحة تسجيل الدخول: ${loginPageResponse.statusCode}';
        return result;
      }

      print('✅ تم الوصول لصفحة تسجيل الدخول بنجاح');

      // الخطوة 2: استخراج الكوكيز والمعلومات المطلوبة
      result['steps'].add('استخراج الكوكيز');
      String? cookie = _extractCookie(loginPageResponse);
      print('🍪 الكوكي: ${cookie ?? "غير موجود"}');

      // الخطوة 3: تجربة طرق تسجيل دخول مختلفة
      result['steps'].add('تجربة طرق تسجيل الدخول');
      
      List<Map<String, dynamic>> loginMethods = [
        // الطريقة 1: مع صفحة login كـ referer
        {
          'name': 'طريقة Login Page',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html#login',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        
        // الطريقة 2: مع صفحة home كـ referer
        {
          'name': 'طريقة Home Page',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html#home',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        
        // الطريقة 3: مع index.html عادي
        {
          'name': 'طريقة Index عادي',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': base64Encode(utf8.encode(password)),
          },
        },
        
        // الطريقة 4: بدون تشفير
        {
          'name': 'بدون تشفير',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html#login',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'password': password,
          },
        },
        
        // الطريقة 5: مع username
        {
          'name': 'مع Username',
          'headers': {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookie ?? '',
            'Accept': '*/*',
            'Origin': routerUrl,
            'Referer': '$routerUrl/index.html#login',
          },
          'body': {
            'isTest': 'false',
            'goformId': 'LOGIN',
            'username': 'admin',
            'password': base64Encode(utf8.encode(password)),
          },
        },
      ];

      // جرب كل طريقة
      for (var method in loginMethods) {
        try {
          print('🔐 جاري تجربة: ${method['name']}');
          
          final loginResponse = await http.post(
            Uri.parse('$routerUrl/goform/goform_set_cmd_process'),
            headers: Map<String, String>.from(method['headers']),
            body: Map<String, String>.from(method['body']),
          ).timeout(const Duration(seconds: 15));

          print('📊 Status Code: ${loginResponse.statusCode}');
          print('📄 Response: ${loginResponse.body}');

          if (loginResponse.statusCode == 200) {
            // فحص نجاح تسجيل الدخول
            bool loginSuccess = _checkLoginSuccess(loginResponse.body);
            
            if (loginSuccess) {
              result['success'] = true;
              result['message'] = 'نجح تسجيل الدخول!';
              result['response_data'] = {
                'status_code': loginResponse.statusCode,
                'body': loginResponse.body,
                'method': method['name'],
                'headers': loginResponse.headers,
              };
              print('✅ نجح تسجيل الدخول بطريقة: ${method['name']}');
              return result;
            } else {
              print('❌ فشل تسجيل الدخول بطريقة: ${method['name']}');
              print('📄 محتوى الاستجابة: ${loginResponse.body}');
            }
          } else {
            print('❌ خطأ HTTP: ${loginResponse.statusCode}');
          }
        } catch (e) {
          print('❌ خطأ في طريقة ${method['name']}: $e');
          continue;
        }
      }

      result['message'] = 'فشلت جميع طرق تسجيل الدخول';
      return result;

    } catch (e) {
      result['message'] = 'خطأ عام: $e';
      print('❌ خطأ عام: $e');
      return result;
    }
  }

  // استخراج الكوكي من الاستجابة
  static String? _extractCookie(http.Response response) {
    return response.headers['set-cookie'] ?? 
           response.headers['Set-Cookie'] ?? 
           response.headers['cookie'] ?? 
           response.headers['Cookie'];
  }

  // فحص نجاح تسجيل الدخول
  static bool _checkLoginSuccess(String responseBody) {
    try {
      // جرب تحليل JSON
      final jsonResponse = json.decode(responseBody);
      
      // فحص أشكال مختلفة للنجاح
      if (jsonResponse['result'] == 'success' ||
          jsonResponse['result'] == '0' ||
          jsonResponse['result'] == 0 ||
          jsonResponse['success'] == true ||
          jsonResponse['success'] == '1' ||
          jsonResponse['login'] == 'success' ||
          jsonResponse['login'] == true) {
        return true;
      }

      // فحص أشكال الفشل
      if (jsonResponse['result'] == 'fail' ||
          jsonResponse['result'] == 'error' ||
          jsonResponse['result'] == '1' ||
          jsonResponse['result'] == 1 ||
          jsonResponse['error'] != null ||
          jsonResponse['login'] == 'fail' ||
          jsonResponse['login'] == false) {
        return false;
      }

    } catch (e) {
      // ليس JSON، فحص نصي
    }

    // فحص نصي للمحتوى
    String body = responseBody.toLowerCase();
    
    // علامات النجاح
    bool hasSuccess = body.contains('success') ||
                     body.contains('ok') ||
                     body.contains('welcome') ||
                     body.contains('home');
    
    // علامات الفشل
    bool hasFailure = body.contains('error') ||
                     body.contains('fail') ||
                     body.contains('invalid') ||
                     body.contains('wrong') ||
                     body.contains('incorrect') ||
                     body.contains('denied');

    // إذا كانت الاستجابة قصيرة وبدون أخطاء، قد تكون نجاح
    bool isShortSuccess = body.length < 50 && !hasFailure;

    return (hasSuccess || isShortSuccess) && !hasFailure;
  }

  // تقرير مفصل للنتائج
  static String formatReport(Map<String, dynamic> result) {
    StringBuffer report = StringBuffer();
    
    report.writeln('🔐 تقرير تسجيل الدخول المخصص');
    report.writeln('=' * 40);
    report.writeln('⏰ الوقت: ${DateTime.now().toIso8601String()}');
    report.writeln('🌐 الراوتر: $routerUrl');
    report.writeln();

    if (result['success']) {
      report.writeln('✅ نجح تسجيل الدخول!');
      
      if (result['response_data'] != null) {
        var responseData = result['response_data'];
        report.writeln('🔧 الطريقة الناجحة: ${responseData['method']}');
        report.writeln('📊 Status Code: ${responseData['status_code']}');
        report.writeln('📄 الاستجابة: ${responseData['body']}');
      }
    } else {
      report.writeln('❌ فشل تسجيل الدخول');
      report.writeln('💬 الرسالة: ${result['message']}');
    }

    report.writeln();
    report.writeln('📋 الخطوات المنفذة:');
    
    List<dynamic> steps = result['steps'] ?? [];
    for (int i = 0; i < steps.length; i++) {
      report.writeln('  ${i + 1}. ${steps[i]}');
    }

    return report.toString();
  }
}
