import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/login_debugger.dart';

class LoginDebugScreen extends StatefulWidget {
  const LoginDebugScreen({super.key});

  @override
  State<LoginDebugScreen> createState() => _LoginDebugScreenState();
}

class _LoginDebugScreenState extends State<LoginDebugScreen> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isDebugging = false;
  Map<String, dynamic>? _diagnosisResult;
  String? _debugReport;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص تسجيل الدخول'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // شرح الأداة
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue[600]),
                        const SizedBox(width: 8),
                        Text(
                          'أداة تشخيص تسجيل الدخول',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الأداة ستساعدك في تشخيص مشكلة تسجيل الدخول عبر:\n'
                      '• اختبار الاتصال بالراوتر\n'
                      '• تجربة كلمات مرور مختلفة\n'
                      '• اختبار طرق تسجيل دخول متعددة\n'
                      '• إعطاء توصيات مفصلة',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // إدخال كلمة المرور
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور للاختبار',
                hintText: 'أدخل كلمة مرور الراوتر',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),

            const SizedBox(height: 16),

            // زر بدء التشخيص
            ElevatedButton.icon(
              onPressed: _isDebugging ? null : _startDiagnosis,
              icon: _isDebugging 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.bug_report),
              label: Text(_isDebugging ? 'جاري التشخيص...' : 'بدء التشخيص'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 16),

            // النتائج
            if (_diagnosisResult != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتائج التشخيص',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              onPressed: _copyReport,
                              icon: const Icon(Icons.copy),
                              tooltip: 'نسخ التقرير',
                            ),
                          ],
                        ),
                        const Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildDiagnosisResults(),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else if (!_isDebugging) ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.bug_report,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'أدخل كلمة المرور واضغط "بدء التشخيص"',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تشخيص مشكلة تسجيل الدخول...'),
                      SizedBox(height: 8),
                      Text(
                        'قد يستغرق هذا بضع ثوان',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _startDiagnosis() async {
    if (_passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال كلمة المرور أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isDebugging = true;
      _diagnosisResult = null;
      _debugReport = null;
    });

    try {
      final result = await LoginDebugger.diagnoseLogin(_passwordController.text);
      final report = LoginDebugger.formatDiagnosisReport(result);

      setState(() {
        _diagnosisResult = result;
        _debugReport = report;
        _isDebugging = false;
      });
    } catch (e) {
      setState(() {
        _isDebugging = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التشخيص: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildDiagnosisResults() {
    if (_diagnosisResult == null) return const SizedBox();

    List<Widget> widgets = [];

    // حالة النجاح/الفشل
    if (_diagnosisResult!.containsKey('successful_password')) {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  const Text(
                    'نجح التشخيص!',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('كلمة المرور الصحيحة: ${_diagnosisResult!['successful_password']}'),
              Text('الطريقة الناجحة: ${_diagnosisResult!['successful_method']}'),
            ],
          ),
        ),
      );
    } else {
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red[600]),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'لم ينجح تسجيل الدخول بأي من الطرق المجربة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      );
    }

    widgets.add(const SizedBox(height: 16));

    // التوصيات
    if (_diagnosisResult!['recommendations'] != null) {
      widgets.add(
        const Text(
          'التوصيات:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
      widgets.add(const SizedBox(height: 8));

      for (String recommendation in _diagnosisResult!['recommendations']) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text('• $recommendation'),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  void _copyReport() {
    if (_debugReport != null) {
      Clipboard.setData(ClipboardData(text: _debugReport!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ التقرير إلى الحافظة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }
}
