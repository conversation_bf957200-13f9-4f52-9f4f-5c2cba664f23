# سجل التغييرات - ZTE Router Control

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مخطط للإضافة
- جدولة إعادة التشغيل
- نسخ احتياطي واستعادة الإعدادات
- دعم راوترات ZTE إضافية
- إشعارات الدفع

## [1.1.0] - 2024-01-22

### أضيف
- **واجهة محسنة مشابهة لـ ZTELink**
  - تصميم جديد للشاشة الرئيسية مع gradients
  - بطاقة معلومات الراوتر محسنة مع أيقونات ملونة
  - عرض معلومات مفصلة (قوة الإشارة، نوع الشبكة، عدد الأجهزة)

- **بطاقة استهلاك البيانات**
  - عرض الاستهلاك اليومي والشهري
  - إحصائيات التحميل والرفع
  - عرض السرعة الحالية
  - أزرار إعادة تعيين الإحصائيات وعرض التفاصيل

- **بطاقة إعدادات الأمان**
  - حظر المواقع
  - التحكم الأبوي
  - جدار الحماية
  - تصفية عناوين MAC
  - فحص الأمان

- **تحسينات تسجيل الدخول**
  - محاولة تسجيل دخول ذكية مع طرق متعددة
  - دعم عناوين IP متعددة للراوتر
  - تجربة كلمات مرور افتراضية تلقائياً
  - رسائل خطأ محسنة مع حلول مقترحة

### غُيّر
- تحسين تصميم الشاشة الرئيسية
- إعادة ترتيب البطاقات حسب الأهمية
- تحسين الألوان والأيقونات

### أُصلح
- مشكلة "كلمة المرور غير صحيحة"
- تحسين استقرار الاتصال
- معالجة أفضل للأخطاء

## [1.0.0] - 2024-01-22

### أضيف
- **شاشة تسجيل الدخول**
  - تسجيل دخول آمن بكلمة مرور الراوتر
  - تشفير كلمة المرور بـ Base64
  - حفظ حالة تسجيل الدخول محلياً
  - واجهة مستخدم عربية بالكامل

- **الشاشة الرئيسية**
  - تصميم حديث مع Material Design 3
  - دعم اللغة العربية (RTL)
  - تحديث تلقائي للبيانات
  - معالجة شاملة للأخطاء

- **التحكم في بيانات الهاتف (LTE)**
  - تشغيل/إيقاف اتصال البيانات
  - عرض حالة الاتصال الحالية
  - مؤشرات بصرية واضحة
  - تأكيدات للإجراءات المهمة

- **إعدادات الواي فاي**
  - عرض اسم الشبكة (SSID) الحالي
  - تغيير اسم الشبكة وكلمة المرور
  - عرض نوع الحماية والتردد
  - تفعيل/تعطيل الواي فاي
  - التحقق من صحة البيانات المدخلة

- **عرض الأجهزة المتصلة**
  - قائمة بجميع الأجهزة المتصلة
  - تمييز نوع الاتصال (سلكي/لاسلكي)
  - عرض قوة الإشارة للأجهزة اللاسلكية
  - عرض عناوين IP و MAC
  - تحديث تلقائي للقائمة

- **التحكم في سرعة الإنترنت**
  - تحديد سرعة الرفع والتحميل
  - تفعيل/تعطيل حدود السرعة
  - واجهة سهلة لإدخال القيم
  - تحويل تلقائي للوحدات

- **إعادة تشغيل الراوتر**
  - إعادة تشغيل الراوتر عن بُعد
  - تأكيد الإجراء لتجنب الأخطاء
  - إشعارات واضحة للمستخدم
  - تسجيل خروج تلقائي بعد إعادة التشغيل

- **إدارة الحالة**
  - استخدام Provider لإدارة الحالة
  - فصل منطق العمل عن واجهة المستخدم
  - إشعارات تلقائية عند تغيير البيانات
  - معالجة شاملة للأخطاء

- **خدمات API**
  - خدمة شاملة للتواصل مع راوتر ZTE
  - إدارة الجلسات والكوكيز
  - معالجة الأخطاء والاستثناءات
  - دعم جميع العمليات المطلوبة

- **نماذج البيانات**
  - نماذج منظمة لجميع البيانات
  - تحويل من وإلى JSON
  - خصائص محسوبة للعرض
  - التحقق من صحة البيانات

- **أدوات مساعدة**
  - ثوابت التطبيق المنظمة
  - مدققات البيانات الشاملة
  - منسقات النصوص والأرقام
  - دعم التشفير والتنسيق

- **الاختبارات**
  - اختبارات الوحدة للمكونات الأساسية
  - اختبارات الويدجت للواجهات
  - اختبارات التكامل للـ Provider
  - تغطية شاملة للوظائف الرئيسية

- **التوثيق**
  - دليل مستخدم شامل
  - دليل مطور مفصل
  - تعليقات كاملة في الكود
  - أمثلة للاستخدام

### التقنيات المستخدمة
- Flutter SDK 3.7.2+
- Provider 6.1.5 لإدارة الحالة
- HTTP 1.4.0 للتواصل مع API
- SharedPreferences 2.5.3 للتخزين المحلي
- Flutter Localizations لدعم اللغة العربية

### المنصات المدعومة
- Android
- iOS
- Web
- Windows
- macOS
- Linux

### متطلبات النظام
- Flutter SDK 3.7.2 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- اتصال بشبكة راوتر ZTE (192.168.0.1)

### الأمان
- تشفير كلمات المرور بـ Base64
- التحقق من صحة جميع المدخلات
- معالجة آمنة للجلسات
- تنظيف البيانات الحساسة

### الأداء
- تحميل سريع للبيانات
- تحديث تلقائي ذكي
- استخدام أمثل للذاكرة
- واجهة مستخدم سلسة

### إمكانية الوصول
- دعم كامل للغة العربية
- تصميم متجاوب لجميع الأحجام
- ألوان متباينة للوضوح
- نصوص واضحة ومقروءة

## الإصدارات القادمة

### [1.1.0] - مخطط
- إضافة إحصائيات استخدام البيانات
- دعم راوترات ZTE إضافية
- تحسينات في الأداء

### [1.2.0] - مخطط
- جدولة إعادة التشغيل
- إعدادات متقدمة للواي فاي
- نسخ احتياطي للإعدادات

### [2.0.0] - مخطط
- إعادة تصميم كاملة للواجهة
- دعم الوضع المظلم
- ميزات إدارة متقدمة

---

## تنسيق سجل التغييرات

### أنواع التغييرات
- `أضيف` للميزات الجديدة
- `غُيّر` للتغييرات في الميزات الموجودة
- `مُهمل` للميزات التي ستُزال قريباً
- `أُزيل` للميزات المُزالة
- `أُصلح` لإصلاح الأخطاء
- `أمان` لإصلاحات الأمان
