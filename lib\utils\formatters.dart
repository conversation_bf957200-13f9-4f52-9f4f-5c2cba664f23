import 'dart:convert';

class Formatters {
  // تنسيق السرعة
  static String formatBandwidth(int bandwidth) {
    if (bandwidth == 0) return 'غير محدود';
    if (bandwidth >= 1024) {
      return '${(bandwidth / 1024).toStringAsFixed(1)} ميجابت/ث';
    }
    return '$bandwidth كيلوبت/ث';
  }

  // تنسيق حجم البيانات
  static String formatDataSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
  }

  // تنسيق قوة الإشارة
  static String formatSignalStrength(int strength) {
    if (strength >= 80) return 'ممتاز ($strength%)';
    if (strength >= 60) return 'جيد ($strength%)';
    if (strength >= 40) return 'متوسط ($strength%)';
    if (strength >= 20) return 'ضعيف ($strength%)';
    return 'ضعيف جداً ($strength%)';
  }

  // تنسيق الوقت المنقضي
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم ${duration.inHours % 24} ساعة';
    }
    if (duration.inHours > 0) {
      return '${duration.inHours} ساعة ${duration.inMinutes % 60} دقيقة';
    }
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    }
    return '${duration.inSeconds} ثانية';
  }

  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    }
    if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    }
    if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    }
    return 'الآن';
  }

  // تنسيق عنوان MAC
  static String formatMACAddress(String mac) {
    if (mac.length == 12) {
      // تحويل من AABBCCDDEEFF إلى AA:BB:CC:DD:EE:FF
      return mac.replaceAllMapped(
        RegExp(r'(.{2})'),
        (match) => '${match.group(1)}:',
      ).substring(0, 17);
    }
    return mac.toUpperCase();
  }

  // تنسيق عنوان IP
  static String formatIPAddress(String ip) {
    // التحقق من صحة عنوان IP وإرجاعه كما هو
    final parts = ip.split('.');
    if (parts.length == 4) {
      return parts.map((part) => int.tryParse(part)?.toString() ?? part).join('.');
    }
    return ip;
  }

  // تنسيق النسبة المئوية
  static String formatPercentage(double value) {
    return '${value.toStringAsFixed(1)}%';
  }

  // تنسيق العملة
  static String formatCurrency(double amount, {String currency = 'جنيه'}) {
    return '${amount.toStringAsFixed(2)} $currency';
  }

  // تنسيق رقم الهاتف
  static String formatPhoneNumber(String phone) {
    // إزالة جميع الأحرف غير الرقمية
    final digits = phone.replaceAll(RegExp(r'\D'), '');
    
    if (digits.length == 11 && digits.startsWith('01')) {
      // رقم مصري
      return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
    }
    if (digits.length == 10) {
      // رقم عادي
      return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
    }
    
    return phone;
  }

  // تشفير كلمة المرور بـ Base64
  static String encodePassword(String password) {
    return base64Encode(utf8.encode(password));
  }

  // فك تشفير كلمة المرور من Base64
  static String decodePassword(String encodedPassword) {
    try {
      return utf8.decode(base64Decode(encodedPassword));
    } catch (e) {
      return encodedPassword;
    }
  }

  // تنسيق اسم الجهاز
  static String formatDeviceName(String name) {
    if (name.isEmpty) return 'جهاز غير معروف';
    
    // تحويل الأحرف الأولى إلى كبيرة
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  // تنسيق نوع الاتصال
  static String formatConnectionType(String type) {
    switch (type.toLowerCase()) {
      case 'wireless':
      case 'wifi':
        return 'لاسلكي';
      case 'wired':
      case 'ethernet':
        return 'سلكي';
      case 'mobile':
      case 'cellular':
        return 'خلوي';
      default:
        return 'غير محدد';
    }
  }

  // تنسيق نوع الأمان
  static String formatSecurityType(String type) {
    switch (type.toUpperCase()) {
      case 'WPA2':
        return 'WPA2';
      case 'WPA3':
        return 'WPA3';
      case 'WEP':
        return 'WEP (غير آمن)';
      case 'OPEN':
      case 'NONE':
        return 'مفتوح (غير آمن)';
      default:
        return type;
    }
  }

  // تنسيق التردد
  static String formatFrequency(String frequency) {
    switch (frequency) {
      case '2.4GHz':
        return '2.4 جيجاهرتز';
      case '5GHz':
        return '5 جيجاهرتز';
      case '6GHz':
        return '6 جيجاهرتز';
      default:
        return frequency;
    }
  }

  // تنسيق حالة الاتصال
  static String formatConnectionStatus(bool isConnected) {
    return isConnected ? 'متصل' : 'غير متصل';
  }

  // تنسيق مستوى البطارية
  static String formatBatteryLevel(int level) {
    if (level >= 80) return 'ممتلئة ($level%)';
    if (level >= 50) return 'جيدة ($level%)';
    if (level >= 20) return 'متوسطة ($level%)';
    return 'منخفضة ($level%)';
  }

  // تنسيق درجة الحرارة
  static String formatTemperature(double temperature) {
    return '${temperature.toStringAsFixed(1)}°م';
  }

  // تنسيق الجهد الكهربائي
  static String formatVoltage(double voltage) {
    return '${voltage.toStringAsFixed(2)} فولت';
  }

  // تنسيق التيار الكهربائي
  static String formatCurrent(double current) {
    return '${current.toStringAsFixed(2)} أمبير';
  }

  // تنسيق القدرة الكهربائية
  static String formatPower(double power) {
    return '${power.toStringAsFixed(2)} واط';
  }

  // تنسيق عدد الأجهزة
  static String formatDeviceCount(int count) {
    if (count == 0) return 'لا توجد أجهزة';
    if (count == 1) return 'جهاز واحد';
    if (count == 2) return 'جهازان';
    if (count <= 10) return '$count أجهزة';
    return '$count جهاز';
  }

  // تنسيق المسافة
  static String formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(0)} متر';
    }
    return '${(meters / 1000).toStringAsFixed(1)} كيلومتر';
  }

  // تنسيق السرعة (كم/ساعة)
  static String formatSpeed(double kmh) {
    return '${kmh.toStringAsFixed(1)} كم/ساعة';
  }
}
