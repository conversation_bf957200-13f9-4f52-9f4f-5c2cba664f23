<?xml version="1.0" encoding="utf-8"?>
<!-- Network Security Configuration for ZTE Router Control -->
<!-- Allows HTTP connections to local router addresses -->
<network-security-config>
    <!-- Allow cleartext traffic for local router addresses -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Local router IP ranges -->
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">************</domain>
        <domain includeSubdomains="false">************</domain>
        <domain includeSubdomains="false">************</domain>
        <domain includeSubdomains="false">********</domain>
        <domain includeSubdomains="false">********38</domain>
        <domain includeSubdomains="false">********</domain>
        <domain includeSubdomains="false">**********</domain>
        <domain includeSubdomains="false">**********</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">***********</domain>
        
        <!-- Allow all local network ranges -->
        <domain includeSubdomains="true">***********/16</domain>
        <domain includeSubdomains="true">10.0.0.0/8</domain>
        <domain includeSubdomains="true">**********/12</domain>
        
        <!-- Localhost -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system certificate authorities -->
            <certificates src="system"/>
            <!-- Trust user-added certificate authorities -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug configuration (only for debug builds) -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
