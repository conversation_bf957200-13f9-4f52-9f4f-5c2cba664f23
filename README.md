# ZTE Router Control

تطبيق Flutter للتحكم الكامل في راوتر ZTE 4G الخاص باتصالات مصر، مشابه لتطبيق ZTELink الرسمي.

![ZTE Router Control](https://img.shields.io/badge/Flutter-3.7.2+-blue.svg)
![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS%20%7C%20Web%20%7C%20Windows-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 📱 لقطات الشاشة

*التطبيق يحتوي على واجهة عربية حديثة مشابهة لتطبيق ZTELink الرسمي*

## المميزات

### 🔐 تسجيل الدخول الآمن
- تسجيل دخول بكلمة مرور الراوتر
- تشفير كلمة المرور بـ Base64
- حفظ حالة تسجيل الدخول

### 📱 التحكم في بيانات الهاتف (LTE)
- تشغيل/إيقاف اتصال البيانات
- عرض حالة الاتصال الحالية
- مؤشرات بصرية واضحة

### 📶 إعدادات الواي فاي
- عرض اسم الشبكة (SSID) الحالي
- تغيير اسم الشبكة وكلمة المرور
- عرض نوع الحماية والتردد
- تفعيل/تعطيل الواي فاي

### 🖥️ الأجهزة المتصلة
- عرض قائمة بجميع الأجهزة المتصلة
- تمييز نوع الاتصال (سلكي/لاسلكي)
- عرض قوة الإشارة للأجهزة اللاسلكية
- عرض عناوين IP و MAC
- تحديث تلقائي للقائمة

### 📊 استهلاك البيانات (جديد!)
- عرض الاستهلاك اليومي والشهري
- إحصائيات التحميل والرفع المفصلة
- عرض السرعة الحالية (تحميل/رفع)
- إعادة تعيين الإحصائيات
- واجهة مشابهة لتطبيق ZTELink

### ⚡ التحكم في السرعة
- تحديد سرعة الرفع والتحميل
- تفعيل/تعطيل حدود السرعة
- واجهة سهلة لإدخال القيم

### 🔄 إعادة تشغيل الراوتر
- إعادة تشغيل الراوتر عن بُعد
- تأكيد الإجراء لتجنب الأخطاء
- إشعارات واضحة للمستخدم
- تسجيل خروج تلقائي بعد إعادة التشغيل

### 🔒 إعدادات الأمان (جديد!)
- حظر المواقع غير المرغوبة
- التحكم الأبوي لحماية الأطفال
- جدار الحماية المتقدم
- تصفية عناوين MAC
- فحص الأمان التلقائي

### 🔧 أدوات التشخيص (جديد!)
- **اختبار الشبكة**: فحص الاتصال بالراوتر
- **تشخيص تسجيل الدخول**: حل مشاكل كلمة المرور
- **تقارير مفصلة**: تشخيص دقيق مع حلول
- **اختبار سريع وشامل**: حسب الحاجة

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Provider**: إدارة الحالة
- **HTTP**: التواصل مع API الراوتر
- **SharedPreferences**: حفظ البيانات محلياً
- **Material Design 3**: التصميم الحديث

## متطلبات التشغيل

- Flutter SDK 3.7.2 أو أحدث
- Dart SDK
- اتصال بشبكة راوتر ZTE (192.168.0.1)

## 📥 التحميل والتثبيت

### تحميل APK جاهز (Android)
- **الحجم**: 21.5 MB
- **الإصدار**: 1.1.0
- **متطلبات**: Android 5.0+ (API 21+)
- **الملف**: `build/app/outputs/flutter-apk/app-release.apk`

### التثبيت من المصدر

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd zte
```

2. **تثبيت التبعيات:**
```bash
flutter pub get
```

3. **تشغيل التطبيق:**
```bash
# للأندرويد
flutter run

# للويب
flutter run -d chrome

# لسطح المكتب (Windows)
flutter run -d windows
```

4. **بناء APK للإنتاج:**
```bash
flutter build apk --release
```

## الاستخدام

### 1. تسجيل الدخول
- تأكد من اتصالك بشبكة الراوتر
- أدخل كلمة مرور الراوتر (افتراضياً: admin)
- اضغط "تسجيل الدخول"

### 2. التحكم في LTE
- استخدم المفتاح لتشغيل/إيقاف بيانات الهاتف
- ستظهر حالة الاتصال فوراً

### 3. إعدادات الواي فاي
- اضغط "تعديل الإعدادات"
- غيّر اسم الشبكة وكلمة المرور
- احفظ التغييرات

### 4. مراقبة الأجهزة
- اعرض قائمة الأجهزة المتصلة
- اضغط "تحديث" لتحديث القائمة

### 5. التحكم في السرعة
- اضغط "تعديل إعدادات السرعة"
- حدد سرعة الرفع والتحميل بالكيلوبت/ثانية
- فعّل التحكم في السرعة

### 6. إعادة التشغيل
- اضغط "إعادة تشغيل الراوتر"
- أكد الإجراء
- انتظر 2-3 دقائق لإعادة التشغيل

## API المستخدم

التطبيق يتواصل مع راوتر ZTE عبر HTTP API:

### تسجيل الدخول
```
POST http://192.168.0.1/goform/goform_set_cmd_process
Body: isTest=false&goformId=LOGIN&password=Base64(password)
```

### التحكم في LTE
```
POST http://192.168.0.1/goform/goform_set_cmd_process
Body: isTest=false&goformId=SET_LTE_USAGE_SWITCH&lte_usage_switch_enable=1
```

### الأجهزة المتصلة
```
GET http://192.168.0.1/goform/goform_get_cmd_process?multi_data=1&cmd=station_list
```

## الاختبار

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبار محدد
flutter test test/widget_test.dart
```

## البناء للإنتاج

```bash
# للأندرويد
flutter build apk --release

# للويب
flutter build web --release

# لسطح المكتب
flutter build windows --release
```

## المساهمة

1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

### حل المشاكل الشائعة
إذا واجهت مشكلة "كلمة المرور غير صحيحة" أو أي مشاكل أخرى، راجع [دليل حل المشاكل](TROUBLESHOOTING.md).

### الحصول على المساعدة
إذا لم تجد حلاً لمشكلتك، يرجى فتح issue في GitHub مع تفاصيل المشكلة.

## ملاحظات مهمة

- ⚠️ تأكد من اتصالك بشبكة الراوتر قبل استخدام التطبيق
- ⚠️ كلمة المرور الافتراضية عادة ما تكون "admin"
- ⚠️ إعادة تشغيل الراوتر تقطع الاتصال مؤقتاً عن جميع الأجهزة
- ⚠️ بعض الميزات قد تختلف حسب موديل الراوتر
