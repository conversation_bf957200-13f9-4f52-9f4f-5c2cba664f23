class BandwidthSettings {
  final int uploadLimit; // in Kbps
  final int downloadLimit; // in Kbps
  final bool isEnabled;
  final String deviceMac; // للتحكم في جهاز محدد (اختياري)

  BandwidthSettings({
    required this.uploadLimit,
    required this.downloadLimit,
    required this.isEnabled,
    this.deviceMac = '',
  });

  factory BandwidthSettings.fromJson(Map<String, dynamic> json) {
    return BandwidthSettings(
      uploadLimit: int.tryParse(json['upload_limit']?.toString() ?? '0') ?? 0,
      downloadLimit: int.tryParse(json['download_limit']?.toString() ?? '0') ?? 0,
      isEnabled: json['is_enabled'] == '1' || json['is_enabled'] == true,
      deviceMac: json['device_mac'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upload_limit': uploadLimit,
      'download_limit': downloadLimit,
      'is_enabled': isEnabled,
      'device_mac': deviceMac,
    };
  }

  BandwidthSettings copyWith({
    int? uploadLimit,
    int? downloadLimit,
    bool? isEnabled,
    String? deviceMac,
  }) {
    return BandwidthSettings(
      uploadLimit: uploadLimit ?? this.uploadLimit,
      downloadLimit: downloadLimit ?? this.downloadLimit,
      isEnabled: isEnabled ?? this.isEnabled,
      deviceMac: deviceMac ?? this.deviceMac,
    );
  }

  String get uploadLimitText {
    if (uploadLimit == 0) return 'غير محدود';
    if (uploadLimit >= 1024) {
      return '${(uploadLimit / 1024).toStringAsFixed(1)} ميجابت/ث';
    }
    return '$uploadLimit كيلوبت/ث';
  }

  String get downloadLimitText {
    if (downloadLimit == 0) return 'غير محدود';
    if (downloadLimit >= 1024) {
      return '${(downloadLimit / 1024).toStringAsFixed(1)} ميجابت/ث';
    }
    return '$downloadLimit كيلوبت/ث';
  }
}
