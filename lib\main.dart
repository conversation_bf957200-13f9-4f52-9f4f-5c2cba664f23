import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

void main() {
  runApp(const RouterControlApp());
}

class RouterControlApp extends StatelessWidget {
  const RouterControlApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تحكم الراوتر ZTE',
      theme: ThemeData.dark(),
      home: const LoginPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final passwordController = TextEditingController();
  String? cookie;
  bool isLoading = false;

  Future<void> login() async {
    setState(() => isLoading = true);

    try {
      // احصل على الكوكي
      final indexResponse = await http.get(
        Uri.parse('http://***********/index.html'),
      );
      cookie = indexResponse.headers['set-cookie'];

      final encodedPassword = base64.encode(
        utf8.encode(passwordController.text.trim()),
      );

      final loginResponse = await http.post(
        Uri.parse('http://***********/goform/goform_set_cmd_process'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookie ?? '',
          'Accept': '*/*',
          'Origin': 'http://***********',
          'Referer': 'http://***********/index.html',
        },
        body: {
          'isTest': 'false',
          'goformId': 'LOGIN',
          'password': encodedPassword,
        },
      );

      setState(() => isLoading = false);

      print('استجابة تسجيل الدخول: ${loginResponse.body}');

      // فحص نجاح تسجيل الدخول
      if (loginResponse.body.contains('success') ||
          loginResponse.body.contains('ok') ||
          loginResponse.body.contains('"result":"0"')) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => HomePage(cookie: cookie ?? '')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل تسجيل الدخول: ${loginResponse.body}')),
        );
      }
    } catch (e) {
      setState(() => isLoading = false);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ في الاتصال: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تسجيل الدخول للراوتر')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(labelText: 'كلمة مرور الراوتر'),
              obscureText: true,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: isLoading ? null : login,
              child:
                  isLoading
                      ? const CircularProgressIndicator()
                      : const Text('تسجيل الدخول'),
            ),
          ],
        ),
      ),
    );
  }
}

class HomePage extends StatefulWidget {
  final String cookie;
  const HomePage({super.key, required this.cookie});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  Map<String, dynamic> routerInfo = {};
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    getRouterInfo();
  }

  Future<void> getRouterInfo() async {
    setState(() => isLoading = true);
    try {
      final response = await http.get(
        Uri.parse(
          'http://***********/goform/goform_get_cmd_process?multi_data=1&cmd=modem_main_state,network_type,network_provider,station_mac,wan_ip_addr,battery_charging,battery_vol_per,battery_pers,realtime_tx_bytes,realtime_rx_bytes,monthly_rx_bytes,monthly_tx_bytes,data_volume_limit_switch,roam_setting_option',
        ),
        headers: {'Cookie': widget.cookie},
      );

      if (response.statusCode == 200) {
        setState(() {
          routerInfo = json.decode(response.body);
        });
        print('معلومات الراوتر: ${response.body}');
      }
    } catch (e) {
      print('خطأ في جلب معلومات الراوتر: $e');
    }
    setState(() => isLoading = false);
  }

  Future<Map<String, dynamic>> sendCommand(Map<String, String> body) async {
    try {
      final response = await http.post(
        Uri.parse('http://***********/goform/goform_set_cmd_process'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': widget.cookie,
          'Accept': '*/*',
          'Origin': 'http://***********',
          'Referer': 'http://***********/index.html',
        },
        body: body,
      );

      print('استجابة الأمر: ${response.body}');

      if (response.statusCode == 200) {
        try {
          return json.decode(response.body);
        } catch (e) {
          return {'result': 'success', 'message': response.body};
        }
      }
      return {'result': 'error', 'message': 'HTTP ${response.statusCode}'};
    } catch (e) {
      print('خطأ في إرسال الأمر: $e');
      return {'result': 'error', 'message': e.toString()};
    }
  }

  Future<void> showResult(String title, Map<String, dynamic> result) async {
    String message =
        result['result'] == 'success' || result['result'] == '0'
            ? 'تم تنفيذ الأمر بنجاح'
            : 'فشل في تنفيذ الأمر: ${result['message'] ?? result['result']}';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$title: $message'),
        backgroundColor:
            result['result'] == 'success' || result['result'] == '0'
                ? Colors.green
                : Colors.red,
      ),
    );
  }

  // أوامر التحكم المحسنة
  Future<void> rebootRouter() async {
    final result = await sendCommand({
      'goformId': 'REBOOT_DEVICE',
      'isTest': 'false',
    });
    showResult('إعادة تشغيل الراوتر', result);
  }

  Future<void> connectInternet() async {
    final result = await sendCommand({
      'goformId': 'CONNECT_NETWORK',
      'notCallback': 'true',
      'isTest': 'false',
    });
    showResult('تشغيل الإنترنت', result);
  }

  Future<void> disconnectInternet() async {
    final result = await sendCommand({
      'goformId': 'DISCONNECT_NETWORK',
      'notCallback': 'true',
      'isTest': 'false',
    });
    showResult('إيقاف الإنترنت', result);
  }

  Future<void> setDataLimit() async {
    final result = await sendCommand({
      'goformId': 'SET_DATA_VOLUME_LIMIT',
      'data_volume_limit_switch': '1',
      'data_volume_limit_size': '10',
      'data_volume_limit_unit': 'GB',
      'data_volume_alert_percent': '80',
      'isTest': 'false',
    });
    showResult('تحديد حد البيانات (10GB)', result);
  }

  Future<void> changeWifiPassword() async {
    final result = await sendCommand({
      'goformId': 'SET_WIFI_INFO',
      'wifi_pwd': 'newpassword123',
      'AuthMode': 'WPA2PSK',
      'EncrypType': 'AES',
      'isTest': 'false',
    });
    showResult('تغيير كلمة مرور الواي فاي', result);
  }

  Future<void> showConnectedDevices() async {
    try {
      final response = await http.get(
        Uri.parse(
          'http://***********/goform/goform_get_cmd_process?multi_data=1&cmd=station_list',
        ),
        headers: {'Cookie': widget.cookie},
      );

      if (response.statusCode == 200 && mounted) {
        final data = json.decode(response.body);
        showDialog(
          context: context,
          builder:
              (_) => AlertDialog(
                title: const Text('الأجهزة المتصلة'),
                content: SingleChildScrollView(child: Text(data.toString())),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في جلب الأجهزة: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم الراوتر'),
        actions: [
          IconButton(onPressed: getRouterInfo, icon: const Icon(Icons.refresh)),
        ],
      ),
      body:
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // معلومات الراوتر
                  if (routerInfo.isNotEmpty) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'معلومات الراوتر:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'حالة الشبكة: ${routerInfo['modem_main_state'] ?? 'غير معروف'}',
                            ),
                            Text(
                              'نوع الشبكة: ${routerInfo['network_type'] ?? 'غير معروف'}',
                            ),
                            Text(
                              'مزود الخدمة: ${routerInfo['network_provider'] ?? 'غير معروف'}',
                            ),
                            Text(
                              'عنوان IP: ${routerInfo['wan_ip_addr'] ?? 'غير معروف'}',
                            ),
                            if (routerInfo['battery_vol_per'] != null)
                              Text(
                                'البطارية: ${routerInfo['battery_vol_per']}%',
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // أزرار التحكم
                  ElevatedButton.icon(
                    onPressed: connectInternet,
                    icon: const Icon(Icons.wifi),
                    label: const Text('تشغيل الإنترنت'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: disconnectInternet,
                    icon: const Icon(Icons.wifi_off),
                    label: const Text('إيقاف الإنترنت'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: changeWifiPassword,
                    icon: const Icon(Icons.lock),
                    label: const Text('تغيير كلمة مرور الواي فاي'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: setDataLimit,
                    icon: const Icon(Icons.data_usage),
                    label: const Text('تحديد حد البيانات (10GB)'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: showConnectedDevices,
                    icon: const Icon(Icons.devices),
                    label: const Text('عرض الأجهزة المتصلة'),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: rebootRouter,
                    icon: const Icon(Icons.restart_alt),
                    label: const Text('إعادة تشغيل الراوتر'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
    );
  }
}
