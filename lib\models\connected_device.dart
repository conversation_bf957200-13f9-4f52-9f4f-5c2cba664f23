class ConnectedDevice {
  final String deviceName;
  final String macAddress;
  final String ipAddress;
  final String connectionType; // 'wireless' or 'wired'
  final int signalStrength;
  final String connectionTime;
  final int downloadSpeed;
  final int uploadSpeed;
  final bool isBlocked;

  ConnectedDevice({
    required this.deviceName,
    required this.macAddress,
    required this.ipAddress,
    required this.connectionType,
    required this.signalStrength,
    required this.connectionTime,
    required this.downloadSpeed,
    required this.uploadSpeed,
    required this.isBlocked,
  });

  factory ConnectedDevice.fromJson(Map<String, dynamic> json) {
    return ConnectedDevice(
      deviceName: json['device_name'] ?? json['hostname'] ?? 'جهاز غير معروف',
      macAddress: json['mac_address'] ?? json['mac'] ?? '',
      ipAddress: json['ip_address'] ?? json['ip'] ?? '',
      connectionType: json['connection_type'] ?? json['type'] ?? 'wireless',
      signalStrength: int.tryParse(json['signal_strength']?.toString() ?? '0') ?? 0,
      connectionTime: json['connection_time'] ?? json['connect_time'] ?? '',
      downloadSpeed: int.tryParse(json['download_speed']?.toString() ?? '0') ?? 0,
      uploadSpeed: int.tryParse(json['upload_speed']?.toString() ?? '0') ?? 0,
      isBlocked: json['is_blocked'] == '1' || json['is_blocked'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_name': deviceName,
      'mac_address': macAddress,
      'ip_address': ipAddress,
      'connection_type': connectionType,
      'signal_strength': signalStrength,
      'connection_time': connectionTime,
      'download_speed': downloadSpeed,
      'upload_speed': uploadSpeed,
      'is_blocked': isBlocked,
    };
  }

  String get connectionTypeArabic {
    switch (connectionType.toLowerCase()) {
      case 'wireless':
      case 'wifi':
        return 'لاسلكي';
      case 'wired':
      case 'ethernet':
        return 'سلكي';
      default:
        return 'غير محدد';
    }
  }

  String get signalStrengthText {
    if (connectionType.toLowerCase() == 'wired' || connectionType.toLowerCase() == 'ethernet') {
      return 'متصل';
    }
    
    if (signalStrength >= 80) return 'ممتاز';
    if (signalStrength >= 60) return 'جيد';
    if (signalStrength >= 40) return 'متوسط';
    if (signalStrength >= 20) return 'ضعيف';
    return 'ضعيف جداً';
  }
}
